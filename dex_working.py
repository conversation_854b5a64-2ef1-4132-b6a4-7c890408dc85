import os
import json
import re
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.agents import initialize_agent
from langchain.agents.agent_types import AgentType
from langchain_core.messages import SystemMessage
from mongo_tool import run_custom_mongo_query, MongoQueryTool

# ✅ Load environment variables
load_dotenv()

# ✅ Initialize Gemini LLM
llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

# ✅ Mongo Query Generator Prompt (Schema-aware, synced with casting layer)
query_instruction = SystemMessage(content="""
You are a MongoDB expert and DeFi data specialist.

You can access the following collections from the 'dex-temp-db':

1. swaps:
   - Fields: account_id, amountInUSD (string), amountOutUSD (string), timestamp (string - UNIX)
   - MUST use `$addFields` to cast `amountInUSD`, `amountOutUSD` to double, and `timestamp` to long

2. deposits:
   - Fields: account_id, amountInUSD (string), tokenIn.symbol, tokenOut.symbol, pool.name, timestamp (string - UNIX)
   - MUST cast `amountInUSD` to double, and `timestamp` to long

3. withdraws:
   - Fields: account_id, amountUSD (string), timestamp (string - UNIX)
   - MUST cast `amountUSD` to double, and `timestamp` to long

4. Z-Score:
   - Fields: wallet_id (string), aggregated_lp_score (int), lp_scores.total_deposit_all_time (double)

⚠️ DO NOT use `ISODate(...)`. Use ISO date strings like "2021-01-01T00:00:00Z" if needed.

Return your output in this strict JSON format:
{
  "collection": "<collection_name>",
  "pipeline": [ ... ]
}

Only output valid JSON. No explanation.
""")

# ✅ LangChain Agent Setup
query_agent = initialize_agent(
    tools=[MongoQueryTool],
    llm=llm,
    agent=AgentType.OPENAI_FUNCTIONS,
    verbose=True,
    agent_kwargs={"system_message": query_instruction}
)

# ✅ CLI Loop
if __name__ == "__main__":
    print("🧠 DeFi Analyst Agent Ready — Ask questions like:")
    print("→ Who are the top 10 depositors?\n")

    while True:
        user_input = input(">> ").strip()
        if user_input.lower() in ["exit", "quit"]:
            break

        try:
            # Step 1: Ask Gemini to generate Mongo query
            result = query_agent.invoke(user_input)

            # Step 2: Parse clean JSON from agent output
            try:
                if isinstance(result, dict) and "collection" in result and "pipeline" in result:
                    collection = result["collection"]
                    pipeline = result["pipeline"]
                elif isinstance(result, str):
                    # Clean markdown and fix Python-style bools
                    json_str = re.sub(r"^```json|```$", "", result.strip(), flags=re.MULTILINE).strip()
                    json_str = json_str.replace("True", "true").replace("False", "false")
                    parsed = json.loads(json_str)
                    collection = parsed["collection"]
                    pipeline = parsed["pipeline"]
                elif "output" in result:
                    cleaned = re.sub(r'ISODate\((.*?)\)', r'\1', result["output"])
                    cleaned = cleaned.replace("True", "true").replace("False", "false")
                    match = re.search(r"\{.*\}", cleaned, re.DOTALL)
                    if not match:
                        raise ValueError("❌ No valid JSON block in LLM output.")
                    parsed = json.loads(match.group(0))
                    collection = parsed["collection"]
                    pipeline = parsed["pipeline"]
                else:
                    raise ValueError("❌ Invalid agent output: missing 'collection' and 'pipeline'.")
            except Exception as parse_error:
                raise ValueError(f"❌ Error parsing JSON from Gemini output: {parse_error}")

            print("\n🛠️ Mongo Query:")
            print(json.dumps({"collection": collection, "pipeline": pipeline}, indent=2))

            # Step 3: Run the query
            raw_result = run_custom_mongo_query(collection, pipeline)

            # Step 4: Format for analysis
            if isinstance(raw_result, str):
                if raw_result.strip().startswith("Error"):
                    raise ValueError(f"MongoDB error: {raw_result}")
                formatted = raw_result
            else:
                formatted = json.dumps(raw_result, indent=2)

            if not formatted.strip() or formatted.strip() == "[]":
                raise ValueError("❌ Empty result returned from Mongo. Cannot interpret.")

            # ✅ Step 5: Add user_input to interpretation instruction
            interpret_instruction = f"""
You are a DeFi analyst. A MongoDB aggregation query was executed on DEX data.

User Question: {user_input}

Analyze the result and summarize all behaviors and tell about trends and what the data can mean for a DEX protocol owners and for normal DEX users in clear DeFi language include volumes wherever possible.
Do not repeat raw data. Provide insights. Also if and only if you are analysing wallet data and providing insights mention those wallets as a list lke Wallets anaysed :-(wallet adresses) i want to see wallets in llm output only 10 at the end of the output in a line whenever wallets are availabe and  if not avaliable please dont display this wallet analysis section in llm output like "Wallets analysed: (No wallet addresses were provided in the input data)" i dont want this or anything like this at all just dot display if not present in the llm output pleasee.
"""

            analysis_prompt = f"{interpret_instruction}\n\nMongoDB Output:\n{formatted}"
            response = llm.invoke(analysis_prompt)

            print("\n🧠 DeFi Insight:\n", response.content)

        except Exception as e:
            print("❌ Error occurred:", str(e))
