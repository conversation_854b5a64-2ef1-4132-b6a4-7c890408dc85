import os
from langchain_fireworks import ChatFireworks

# Set your Fireworks API key here (or use env variable)
os.environ["FIREWORKS_API_KEY"] = "fw_3ZZaG3tyAt8b7GeGQfegakQU"  # replace this

# Initialize Fireworks LLM
llm = ChatFireworks(
    model="accounts/sentientfoundation-serverless/models/dobby-mini-unhinged-plus-llama-3-1-8b",  
    temperature=0.2,
    max_tokens=1024
)

# Test prompt
response = llm.invoke("What are the most common user actions in a lending protocol like Aave?")
print("🧠 LLM Response:\n", response.content)
