import os
import json
import re
import traceback
from dotenv import load_dotenv
from mongo_tool import run_custom_mongo_query
from lp_usecases import handle_lp_usecase
from query_parser import parse_prompt_llm
from llm_utils import generate_llm_prompt
from llm_agent import llm, query_agent
import logging

# Configure logging
logging.basicConfig(filename="app.log", level=logging.DEBUG, format="%(asctime)s - %(levelname)s - %(message)s")

# Load environment variables
load_dotenv()

# Error suggestion message for failed or off-topic queries
ERROR_SUGGESTION_MESSAGE = """
Sorry, I can't answer this. Instead, try asking something like:
- ****************************************** how much gas fees has this wallet paid on dex?
-  Rank top OG wallets by cumulative gas fees paid on this DEX.
- analyse top 10 wallets on aave protocol with scores between 800 ad 900
- top 10 traders who trade with usdc and weth 
- What tokens are most commonly swapped
"""

# Classifier
def classify_prompt_llm(prompt: str) -> str:
    classification_prompt = f"""
You are a DeFi expert AI. Classify the following user query into one of three categories:

- "dex" — for queries about swaps, deposits, withdrawals, DEX volumes, traders, or liquidity pools.
- "lp" — for queries about lending protocols like Aave, Compound, borrowing, lending, zScore, wallet behavior,liquidation or creditworthiness.
- "off_topic" — for greetings, small talk, or unrelated queries (e.g., "hi," "hello," "I love you").

Return only one word: dex, lp, or off_topic.

Query:
{prompt}
"""
    try:
        result = llm.invoke(classification_prompt)
        answer = result.content.strip().lower()
        logging.info(f"Classified prompt '{prompt}' as: {answer}")
        if answer not in ["dex", "lp", "off_topic"]:
            logging.warning(f"Invalid classification '{answer}' for prompt: {prompt}, defaulting to off_topic")
            return "off_topic"
        return answer
    except Exception as e:
        logging.error(f"Classification failed: {str(e)}")
        print(f"❌ Classification failed: {str(e)}")
        return "off_topic"  # Default to off_topic for safety

# Unified entrypoint
def query_agent_function(user_input: str) -> str:
    try:
        query_type = classify_prompt_llm(user_input)
        logging.info(f"Query Type: {query_type}, Prompt: {user_input}")
        print(f"📜 Query Type: {query_type}")

        if query_type == "off_topic":
            off_topic_prompt = f"""
You are a friendly DeFi assistant. The user said: "{user_input}". 
Respond in a warm, natural way. If it's a greeting or small talk, reply kindly emphasising you are a defi agent. 
Then, redirect them to ask questions related to our use cases. Include these exact examples:
- List the top 10 most active wallets in Aave based on borrow and repay activity.
- What’s the lending behavior of ****************************************** on Aave?
- Analyze the risk profile of wallet 0x000abc123def... on Compound V3?
- List wallets whose single largest swap exceeded $100k
- What tokens are most commonly swapped in dex.
Do not add any other examples or suggestions it should be the above onces word by word. Keep the response friendly.
"""
            response = llm.invoke(off_topic_prompt)
            cleaned = re.sub(r":\*-", ":", response.content)
            cleaned = re.sub(r":\*", ":", cleaned)
            cleaned = cleaned.replace("*-", "")
            cleaned = cleaned.replace("* ", "- ")
            cleaned = cleaned.replace("**", "")
            logging.info(f"Off-topic response generated for prompt: {user_input}")
            return cleaned

        if query_type == "dex":
            result = query_agent.invoke(user_input)

            if isinstance(result, dict) and "collection" in result:
                collection = result["collection"]
                pipeline = result["pipeline"]
            elif isinstance(result, str):
                json_str = re.sub(r"^```json|```$", "", result.strip(), flags=re.MULTILINE).strip()
                json_str = json_str.replace("True", "true").replace("False", "false")
                try:
                    parsed = json.loads(json_str)
                    collection = parsed["collection"]
                    pipeline = parsed["pipeline"]
                except json.JSONDecodeError as e:
                    logging.error(f"JSON parse failed: {str(e)}, Input: {json_str}")
                    return ERROR_SUGGESTION_MESSAGE
            elif "output" in result:
                cleaned = re.sub(r'ISODate\((.*?)\)', r'\1', result["output"])
                cleaned = cleaned.replace("True", "true").replace("False", "false")
                match = re.search(r"\{.*\}", cleaned, re.DOTALL)
                if not match:
                    logging.error("No valid JSON block in LLM output")
                    return ERROR_SUGGESTION_MESSAGE
                try:
                    parsed = json.loads(match.group(0))
                    collection = parsed["collection"]
                    pipeline = parsed["pipeline"]
                except json.JSONDecodeError as e:
                    logging.error(f"JSON parse failed: {str(e)}, Input: {cleaned}")
                    return ERROR_SUGGESTION_MESSAGE
            else:
                logging.error("Invalid agent output: missing 'collection' and 'pipeline'")
                return ERROR_SUGGESTION_MESSAGE

            logging.info(f"Executing MongoDB pipeline: {json.dumps(pipeline, indent=2)}")
            raw_result = run_custom_mongo_query(collection, pipeline)
            if raw_result.startswith("Error"):
                logging.error(f"MongoDB query failed: {raw_result}")
                return ERROR_SUGGESTION_MESSAGE

            formatted = json.dumps(json.loads(raw_result), indent=2) if isinstance(raw_result, str) else json.dumps(raw_result, indent=2)
            if not formatted.strip() or formatted.strip() == "[]":
                logging.error("Empty result returned from Mongo")
                return ERROR_SUGGESTION_MESSAGE

            interpret_instruction = f"""
You are a DeFi analyst. A MongoDB aggregation query was executed on DEX data.

User Question: {user_input}

Analyze the result and summarize behaviors, trends, and insights for DEX protocol owners and users in clear DeFi language, including volumes where possible.
Do not repeat raw data. If analyzing wallet data, list wallets as: Wallets Analyzed: (addresses).
"""
            analysis_prompt = f"{interpret_instruction}\n\nMongoDB Output:\n{formatted}"
            response = llm.invoke(analysis_prompt)
            logging.info(f"DEX response generated for prompt: {user_input}")
            return response.content

        else:
            try:
                parsed = parse_prompt_llm(user_input)
                response = handle_lp_usecase(
                    wallet=parsed.wallet_address,
                    score=None,
                    memory={
                        "protocol": parsed.protocols[0] if parsed.protocols else "aave",
                        "protocol_1": parsed.protocols[0] if parsed.protocols else "aave",
                        "protocol_2": parsed.protocols[1] if len(parsed.protocols) > 1 else "compound_v2",
                        "min_score": parsed.score_range[0],
                        "max_score": parsed.score_range[1],
                        "_raw_prompt": user_input
                    },
                    use_case=parsed.use_case,
                    top_n=parsed.top_n,
                    protocol=parsed.protocols[0] if parsed.protocols else "aave",
                    min_score=parsed.score_range[0],
                    max_score=parsed.score_range[1]
                )

                prompt = response.get("group_prompt")

                if not prompt:
                    prompt = generate_llm_prompt(
                        wallet=parsed.wallet_address,
                        score=None,
                        memory=response.get("memory", {}),
                        use_case=parsed.use_case
                    )

                logging.info(f"LP prompt generated: {prompt}")
                result = llm.invoke(prompt)
                logging.info(f"LP response generated for prompt: {user_input}")
                return result.content
            except Exception as e:
                stack_trace = traceback.format_exc()
                logging.error(f"LP query processing failed: {str(e)}\nStack Trace:\n{stack_trace}\nParsed: {vars(parsed) if 'parsed' in locals() else 'Not parsed'}")
                return ERROR_SUGGESTION_MESSAGE

    except Exception as e:
        stack_trace = traceback.format_exc()
        logging.error(f"Query processing failed: {str(e)}\nStack Trace:\n{stack_trace}\nParsed: {vars(parsed) if 'parsed' in locals() else 'Not parsed'}")
        return ERROR_SUGGESTION_MESSAGE
