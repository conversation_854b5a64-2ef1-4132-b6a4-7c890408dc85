[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "defi-analytics-agent"
version = "1.0.0"
description = "DeFi Analytics Agent - AI-powered DeFi data analysis"
authors = [
    {name = "DeFi Analytics Team", email = "<EMAIL>"}
]
readme = "docs/README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "fastapi>=0.100.0",
    "uvicorn>=0.20.0",
    "pymongo>=4.0.0",
    "python-dotenv>=1.0.0",
    "langchain>=0.1.0",
    "langchain-core>=0.1.7",
    "langchain-google-genai>=1.0.0",
    "langchain-fireworks>=0.1.0",
    "google-generativeai>=0.3.0",
    "openai>=1.0.0",
    "pydantic>=2.0.0",
    "requests>=2.25.0",
    "typing-extensions>=4.0.0",
    "python-multipart>=0.0.5",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=22.0.0",
    "isort>=5.0.0",
    "flake8>=4.0.0",
    "mypy>=0.991",
]

[project.urls]
Homepage = "https://github.com/zerufinance/zscore-sentient-agent"
Repository = "https://github.com/zerufinance/zscore-sentient-agent"
Documentation = "https://github.com/zerufinance/zscore-sentient-agent/blob/main/docs/README.md"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
