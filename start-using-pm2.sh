#!/bin/bash

echo "🔄 Starting Uvicorn via PM2..."
APP_NAME="agentapi"
COMMAND="uvicorn main:app --host 0.0.0.0 --port 8001"

# Start the process
pm2 start "$COMMAND" --name "$APP_NAME"

# Show status
if [ $? -eq 0 ]; then
  echo "✅ PM2 process '$APP_NAME' started successfully."
else
  echo "❌ Failed to start '$APP_NAME'. Check PM2 logs."
fi

# Show PM2 process list
echo "📋 Current PM2 processes:"
pm2 list

# Optional: tail logs
echo "📝 Tailing logs for '$APP_NAME' (press Ctrl+C to exit)..."
pm2 logs "$APP_NAME"
