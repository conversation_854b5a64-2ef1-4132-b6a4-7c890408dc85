[Unit]
Description=DeFi Analytics Agent FastAPI Application
After=network.target

[Service]
Type=exec
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/zscore-sentient-agent
Environment=PATH=/home/<USER>/zscore-sentient-agent/venv/bin
ExecStart=/home/<USER>/zscore-sentient-agent/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8001 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=defi-agent

[Install]
WantedBy=multi-user.target
