# Core web framework
fastapi>=0.100.0
uvicorn>=0.20.0

# Database
pymongo>=4.0.0

# Environment management
python-dotenv>=1.0.0

# LangChain and LLM integrations
langchain>=0.1.0
langchain-core>=0.1.7
langchain-google-genai>=1.0.0
langchain-fireworks>=0.1.0

# Google Generative AI
google-generativeai>=0.3.0

# OpenAI (for Fireworks compatibility)
openai>=1.0.0

# Data validation
pydantic>=2.0.0

# HTTP requests
requests>=2.25.0

# Logging and utilities
typing-extensions>=4.0.0

# Additional dependencies that might be needed
python-multipart>=0.0.5
