#!/bin/bash

# HTTPS Setup Script for DeFi Analytics Agent
# This script sets up Nginx reverse proxy with SSL certificates

set -e

echo "🔒 Setting up HTTPS for DeFi Analytics Agent"
echo "============================================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root. Please run as a regular user with sudo privileges."
   exit 1
fi

# Check if domain is provided
DOMAIN=${1:-"agentapi.myzscore.ai"}
print_status "Setting up HTTPS for domain: $DOMAIN"

# Update system packages
print_status "Updating system packages..."
sudo apt update

# Install Nginx
print_status "Installing Nginx..."
sudo apt install -y nginx

# Install Certbot for Let's Encrypt
print_status "Installing Certbot for SSL certificates..."
sudo apt install -y certbot python3-certbot-nginx

# Stop Nginx temporarily
print_status "Stopping Nginx temporarily..."
sudo systemctl stop nginx

# Create Nginx configuration
print_status "Creating Nginx configuration..."
sudo tee /etc/nginx/sites-available/$DOMAIN > /dev/null << 'EOF'
# Temporary HTTP configuration for certificate generation
server {
    listen 80;
    server_name agentapi.myzscore.ai;
    
    location / {
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Enable the site
print_status "Enabling Nginx site..."
sudo ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
print_status "Testing Nginx configuration..."
sudo nginx -t

# Start Nginx
print_status "Starting Nginx..."
sudo systemctl start nginx
sudo systemctl enable nginx

# Make sure the FastAPI app is running
print_status "Checking if FastAPI application is running..."
if ! curl -s http://localhost:8001/health > /dev/null; then
    print_warning "FastAPI application is not running. Starting it now..."
    print_status "Please make sure to run './run-production.sh' in another terminal before continuing."
    read -p "Press Enter when the FastAPI application is running on port 8001..."
fi

# Obtain SSL certificate
print_status "Obtaining SSL certificate from Let's Encrypt..."
sudo certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL>

# Update Nginx configuration with full HTTPS setup
print_status "Updating Nginx configuration with full HTTPS setup..."
sudo cp nginx-config.conf /etc/nginx/sites-available/$DOMAIN

# Test updated configuration
print_status "Testing updated Nginx configuration..."
sudo nginx -t

# Reload Nginx
print_status "Reloading Nginx..."
sudo systemctl reload nginx

# Set up automatic certificate renewal
print_status "Setting up automatic certificate renewal..."
sudo systemctl enable certbot.timer
sudo systemctl start certbot.timer

# Configure firewall
print_status "Configuring firewall..."
sudo ufw allow 'Nginx Full'
sudo ufw allow 22
sudo ufw --force enable

print_success "HTTPS setup completed successfully!"
echo ""
echo "🎉 Your API is now available at:"
echo "  - https://$DOMAIN/health"
echo "  - https://$DOMAIN/query"
echo ""
echo "🔒 SSL certificate will auto-renew every 90 days"
echo "📊 Nginx logs are available at:"
echo "  - Access: /var/log/nginx/$DOMAIN.access.log"
echo "  - Error: /var/log/nginx/$DOMAIN.error.log"
echo ""
echo "🚀 To start your application:"
echo "  ./run-production.sh"
echo ""
print_warning "Make sure your domain DNS is pointing to this server's IP address!"
