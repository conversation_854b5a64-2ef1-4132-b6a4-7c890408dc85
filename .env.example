# DeFi Analytics Agent Environment Variables
# Copy this file to .env and fill in your actual values

# API Configuration
API_HOST=0.0.0.0
API_PORT=8001

# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017

# LLM API Keys
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
FIREWORKS_API_KEY=your_fireworks_api_key_here

# External API Keys
ZPASS_API_KEY=your_zpass_api_key_here

# Logging Configuration
LOG_LEVEL=INFO
