
import os
import re
import json
from dotenv import load_dotenv
from typing import List, Optional
from pydantic import BaseModel, Field
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import PromptTemplate
from config.settings import GOOGLE_API_KEY
import logging

# Configure logging
logging.basicConfig(filename="app.log", level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
llm_log_handler = logging.FileHandler("llm_output.log")
llm_log_handler.setLevel(logging.DEBUG)
llm_logger = logging.getLogger("llm_output")
llm_logger.addHandler(llm_log_handler)

# Load .env
load_dotenv()
if "api_key" in os.environ:
    os.environ["GOOGLE_API_KEY"] = os.getenv("api_key")

# Define supported classes
class QueryParseOutput(BaseModel):
    use_case: str
    protocols: List[str]
    score_range: List[int] = Field(default_factory=lambda: [0, 1000])
    wallet_address: Optional[str] = None
    top_n: int = 5
    conditions: List[dict] = Field(default_factory=list)  # e.g., [{"high": "deposit"}, {"low": "liquidation"}]

parser = PydanticOutputParser(pydantic_object=QueryParseOutput)

# Simplified prompt template - avoiding JSON conflicts by using different condition format
prompt_template = PromptTemplate(
    template="""
You are an intelligent assistant that extracts DeFi queries for Lending Protocol analysis. Return a valid JSON object matching the Pydantic schema for QueryParseOutput. Do NOT include any text outside the JSON object.

Supported use_case values:
- lp_protocol_comparison (for comparing protocols, e.g., "compare aave and compound_v3")
- lp_wallet_behavior_analysis (for single wallet, e.g., "how does wallet 0x... behave")
- lp_score_filtered_wallets (for listing wallets by score or activity, e.g., "top 10 wallets", "liquidations", "deposits")
- airdrop (for airdrop eligibility, e.g., "airdrop for 0x...")
- off_topic (for non-DeFi queries)
- unknown (if unclear)

Rules:
- If the prompt mentions multiple protocols, set use_case to "lp_protocol_comparison".
- If the prompt includes a wallet address and a protocol, set use_case to "lp_wallet_behavior_analysis".
- If the prompt asks for a list of wallets (e.g., "top 10 wallets", "top 10 users", "z-scores", "liquidation", "deposit", "volatile", "cluster", "time", "days"), set use_case to "lp_score_filtered_wallets".
- Extract protocols (e.g., ["aave", "compound_v2", "compound_v3"]) from the prompt.
- Extract wallet_address if a valid Ethereum address (0x...) is present.
- Extract top_n if "top N" is mentioned, default to 5.
- Extract score_range if "z-scores between X and Y" or "scores from X to Y" is mentioned, default to [0, 1000].
- For conditions, use format: "condition_type:metric" (e.g., "high deposit" -> "high_metric:deposit", "low liquidation" -> "low_metric:liquidation").

Examples:
- Prompt: "compare aave and compound v3 based on volatile assets and withdrawals"
  Output: {{"use_case": "lp_protocol_comparison", "protocols": ["aave", "compound_v3"], "score_range": [0, 1000], "wallet_address": null, "top_n": 5, "conditions": []}}
- Prompt: "how does wallet ****************************************** behave in compound v3 including all activity metrics"
  Output: {{"use_case": "lp_wallet_behavior_analysis", "protocols": ["compound_v3"], "score_range": [0, 1000], "wallet_address": "******************************************", "top_n": 5, "conditions": []}}
- Prompt: "Top 10 users on Aave with scores between 200 and 300"
  Output: {{"use_case": "lp_score_filtered_wallets", "protocols": ["aave"], "score_range": [200, 300], "wallet_address": null, "top_n": 10, "conditions": []}}
- Prompt: "List the top 5 wallets in Aave with high deposit and low liquidation"
  Output: {{"use_case": "lp_score_filtered_wallets", "protocols": ["aave"], "score_range": [0, 1000], "wallet_address": null, "top_n": 5, "conditions": ["high_metric:deposit", "low_metric:liquidation"]}}

Prompt:
{user_prompt}
""",
    input_variables=["user_prompt"]
)

# Initialize LLM
llm = ChatGoogleGenerativeAI(model="gemini-1.5-flash", temperature=0.1)

# Parsing function
def parse_prompt_llm(prompt: str) -> QueryParseOutput:
    # Hardcode for top-N wallet/user queries with z-scores
    zscore_pattern = r"(?i)top\s+(\d+)\s+(users|wallets|liquidity\s+providers)\s+(?:on|in)\s+(aave|compound\s+v2|compound\s+v3)\s*(?:with\s+)?scores\s+(?:between|from)\s+(\d+)\s+(?:and|to)\s+(\d+)\s*$"
    if re.search(zscore_pattern, prompt, re.IGNORECASE):
        match = re.search(zscore_pattern, prompt, re.IGNORECASE)
        top_n = int(match.group(1))
        protocol_raw = match.group(3).lower().replace(" ", "_")
        protocol = "compound_v2" if protocol_raw == "compound_v2" else protocol_raw
        protocol = "compound_v3" if protocol_raw == "compound_v3" else protocol
        min_score = int(match.group(4))
        max_score = int(match.group(5))
        logging.info(f"Bypassing LLM for z-score query: top_n={top_n}, protocol={protocol}, score_range=[{min_score}, {max_score}]")
        print(f"📜 Bypassing LLM for z-score query: top_n={top_n}, protocol={protocol}, score_range=[{min_score}, {max_score}]")
        return QueryParseOutput(
            use_case="lp_score_filtered_wallets",
            protocols=[protocol],
            score_range=[min_score, max_score],
            wallet_address=None,
            top_n=top_n,
            conditions=[]
        )

    # Hardcode for top-N wallet queries with high/low conditions
    if re.search(r"top\s+\d+\s+(users|wallets)\s+(on|in)\s+(aave|compound\s*(v2|v3)?)", prompt, re.IGNORECASE):
        match = re.search(r"top\s+(\d+)", prompt, re.IGNORECASE)
        top_n = int(match.group(1)) if match else 5
        protocols = []
        if "aave" in prompt.lower():
            protocols.append("aave")
        if re.search(r"compound\s*v3", prompt, re.IGNORECASE):
            protocols.append("compound_v3")
        elif re.search(r"compound\s*v2", prompt, re.IGNORECASE) or "compound" in prompt.lower():
            protocols.append("compound_v2")
        
        # Extract high/low conditions
        conditions = []
        activity_keywords = ["borrow", "repay", "liquidation", "deposit", "supply", "withdraw", "mix", "volatile"]
        for keyword in activity_keywords:
            if f"high {keyword}" in prompt.lower():
                field = f"{keyword}_mix(%volatile)" if "volatile" in keyword else keyword
                conditions.append({"high": field})
            if f"low {keyword}" in prompt.lower():
                field = f"{keyword}_mix(%volatile)" if "volatile" in keyword else keyword
                conditions.append({"low": field})

        logging.info(f"Bypassing LLM for top-N query: top_n={top_n}, protocols={protocols}, conditions={conditions}")
        print(f"📜 Bypassing LLM for top-N query: top_n={top_n}, protocols={protocols}, conditions={conditions}")
        return QueryParseOutput(
            use_case="lp_score_filtered_wallets",
            protocols=protocols,
            score_range=[0, 1000],
            wallet_address=None,
            top_n=top_n,
            conditions=conditions
        )

    # Hardcode for protocol comparison
    if re.search(r"(?i)compare\s+(aave|compound\s+v2|compound\s+v3)\s+(?:and|vs)\s+(aave|compound\s+v2|compound\s+v3)\s*(?:protocol\s+usage|activity)?", prompt, re.IGNORECASE):
        match = re.search(r"(?i)compare\s+(aave|compound\s+v2|compound\s+v3)\s+(?:and|vs)\s+(aave|compound\s+v2|compound\s+v3)\s*(?:protocol\s+usage|activity)?", prompt, re.IGNORECASE)
        protocols = []
        protocol1_raw = match.group(1).lower().replace(" ", "_")
        protocol1 = "compound_v2" if protocol1_raw == "compound_v2" else protocol1_raw
        protocol1 = "compound_v3" if protocol1_raw == "compound_v3" else protocol1
        protocols.append(protocol1)
        protocol2_raw = match.group(2).lower().replace(" ", "_")
        protocol2 = "compound_v2" if protocol2_raw == "compound_v2" else protocol2_raw
        protocol2 = "compound_v3" if protocol2_raw == "compound_v3" else protocol2
        protocols.append(protocol2)
        logging.info(f"Bypassing LLM for comparison query: protocols={protocols}")
        print(f"📜 Bypassing LLM for comparison query: {protocols}")
        return QueryParseOutput(
            use_case="lp_protocol_comparison",
            protocols=protocols,
            score_range=[0, 1000],
            wallet_address=None,
            top_n=5,
            conditions=[]
        )

    # Hardcode for z-score, activity, or cluster queries
    activity_keywords = r"(borrow|repay|liquidation|deposit|supply|withdraw|time|days|mix|volatile|cluster|z-score|zscore)"
    if re.search(activity_keywords, prompt, re.IGNORECASE):
        match = re.search(r"z-scores?\s+(between|from)\s+(\d+)\s+(and|to)\s+(\d+)", prompt, re.IGNORECASE)
        min_score = int(match.group(2)) if match else 0
        max_score = int(match.group(4)) if match else 1000
        top_n_match = re.search(r"top\s+(\d+)", prompt, re.IGNORECASE)
        top_n = int(top_n_match.group(1)) if top_n_match else 5
        protocols = []
        if "aave" in prompt.lower():
            protocols.append("aave")
        if re.search(r"compound\s*v3", prompt, re.IGNORECASE):
            protocols.append("compound_v3")
        elif re.search(r"compound\s*v2", prompt, re.IGNORECASE) or "compound" in prompt.lower():
            protocols.append("compound_v2")
        
        # Extract high/low conditions
        conditions = []
        for keyword in ["borrow", "repay", "liquidation", "deposit", "supply", "withdraw", "mix", "volatile"]:
            if f"high {keyword}" in prompt.lower():
                field = f"{keyword}_mix(%volatile)" if "volatile" in keyword else keyword
                conditions.append({"high": field})
            if f"low {keyword}" in prompt.lower():
                field = f"{keyword}_mix(%volatile)" if "volatile" in keyword else keyword
                conditions.append({"low": field})

        logging.info(f"Bypassing LLM for activity query: top_n={top_n}, protocols={protocols}, score_range=[{min_score}, {max_score}], conditions={conditions}")
        print(f"📜 Bypassing LLM for activity query: top_n={top_n}, protocols={protocols}, score_range=[{min_score}, {max_score}], conditions={conditions}")
        return QueryParseOutput(
            use_case="lp_score_filtered_wallets",
            protocols=protocols,
            score_range=[min_score, max_score],
            wallet_address=None,
            top_n=top_n,
            conditions=conditions
        )

    # Extract wallet address for behavior analysis
    wallet_match = re.search(r"0x[a-fA-F0-9]{40}", prompt)
    if wallet_match and any(p in prompt.lower() for p in ["aave", "compound", "compound v3"]):
        wallet_address = wallet_match.group(0)
        protocols = []
        if "aave" in prompt.lower():
            protocols.append("aave")
        if re.search(r"compound\s*v3", prompt, re.IGNORECASE):
            protocols.append("compound_v3")
        elif re.search(r"compound\s*v2", prompt, re.IGNORECASE) or "compound" in prompt.lower():
            protocols.append("compound_v2")
        logging.info(f"Bypassing LLM for wallet behavior query: wallet={wallet_address}, protocols={protocols}")
        print(f"📜 Bypassing LLM for wallet behavior query: wallet={wallet_address}, protocols={protocols}")
        return QueryParseOutput(
            use_case="lp_wallet_behavior_analysis",
            protocols=protocols,
            score_range=[0, 1000],
            wallet_address=wallet_address,
            top_n=5,
            conditions=[]
        )

    # LLM parsing for other queries
    formatted = prompt_template.format(user_prompt=prompt)
    logging.info(f"LLM input: {formatted}")
    try:
        result = llm.invoke(formatted)
        result_content = result.content.strip()
        llm_logger.debug(f"Raw LLM response: {result_content}")

        # Clean JSON
        json_str = re.sub(r"^```json|```$", "", result_content, flags=re.MULTILINE).strip()
        json_str = re.sub(r"[\n\r\t]+", "", json_str)
        json_str = json_str.replace("'", '"').replace("None", "null")
        json_str = re.sub(r'(\w+)(?=\s*:)', r'"\1"', json_str)
        parsed_json = json.loads(json_str)
        parsed = parser.parse(json.dumps(parsed_json))

        # Sanitize conditions to handle string format and convert to expected dict format
        sanitized_conditions = []
        for condition in parsed.conditions:
            if isinstance(condition, str) and ":" in condition:
                # Handle "high_metric:deposit" or "low_metric:liquidation" format
                condition_type, metric = condition.split(":", 1)
                if condition_type == "high_metric":
                    sanitized_conditions.append({"high": metric})
                elif condition_type == "low_metric":
                    sanitized_conditions.append({"low": metric})
                else:
                    sanitized_conditions.append(condition)
            elif isinstance(condition, dict):
                # Handle existing dict format
                for key, value in condition.items():
                    if key in ["high_metric", "low_metric"] and isinstance(value, str):
                        condition_type = "high" if key == "high_metric" else "low"
                        sanitized_conditions.append({condition_type: value})
                    else:
                        sanitized_conditions.append(condition)
            else:
                sanitized_conditions.append(condition)
        parsed.conditions = sanitized_conditions

        logging.info(f"Parsed Prompt: {parsed}")
        print(f"📜 Parsed Prompt: {parsed}")

        # Sanitize score_range
        if not isinstance(parsed.score_range, list) or len(parsed.score_range) != 2:
            parsed.score_range = [0, 1000]

        return parsed

    except Exception as e:
        logging.error(f"LLM parsing failed: {str(e)}")
        print(f"❌ LLM parsing failed: {str(e)}")
        return QueryParseOutput(
            use_case="unknown",
            protocols=[],
            score_range=[0, 1000],
            wallet_address=None,
            top_n=5,
            conditions=[]
        )
