# Database connection management

from pymongo import MongoClient
from config.settings import MONGO_URI, DB_NAMES

# Return database instance
def get_db(db_name="default"):
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
        client.server_info()  # Check connection
        return client[DB_NAMES.get(db_name, DB_NAMES["default"])]
    except Exception as e:
        raise ConnectionError(f"Failed to connect to MongoDB: {str(e)}")