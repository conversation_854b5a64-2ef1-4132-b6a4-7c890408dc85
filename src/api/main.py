from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
import sys

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.core.agent import query_agent_function
from config.settings import CORS_ORIGINS

app = FastAPI(title="DEX DeFi Analyst Agent API")

# Add CORS middleware with specific origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Health check endpoint
@app.get("/health")
def health_check():
    return {"status": "✅ DEX Analyst Agent is running"}

# Request body schema
class QueryRequest(BaseModel):
    prompt: str

# Agent query endpoint
@app.post("/query")
def run_agent_query(request: QueryRequest):
    try:
        result = query_agent_function(request.prompt)
        return {"result": result}
    except Exception as e:
        return {"error": str(e)}
