# ZPass API client

import requests
import os
import json
from typing import Dict
from pydantic import BaseModel
from langchain.tools import StructuredTool
from dotenv import load_dotenv
from config.settings import ZPASS_API_KEY

load_dotenv()

class ZPassInput(BaseModel):
    protocol_name: str
    min_score: int
    max_score: int
    limit: int = 10

def query_zpass_wallets(protocol_name: str, min_score: int, max_score: int, limit: int = 10) -> str:
    try:
        url = "https://api.myzscore.ai/zpass/api/zscore/wallets/protocol"
        params = {
            "protocol_name": protocol_name,
            "min_score": min_score,
            "max_score": max_score,
            "limit": limit
        }
        headers = {
            "accept": "application/json",
            "x-api-key": ZPASS_API_KEY
        }
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        return json.dumps(response.json(), indent=2)
    except Exception as e:
        return f"Error calling ZPass API: {str(e)}"

ZPassWalletTool = StructuredTool.from_function(
    func=query_zpass_wallets,
    name="ZPassWalletTool",
    description="Get wallets from a lending protocol by score range. Useful for LP-related queries like 'top users on Aave with score above 700'.",
    args_schema=ZPassInput
)
