import os
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.agents import initialize_agent
from langchain.agents.agent_types import AgentType
from langchain_core.messages import SystemMessage
from src.database.mongo_client import MongoQueryTool
from config.settings import GOOGLE_API_KEY

# ✅ Load environment variables
load_dotenv()

# ✅ Initialize Gemini LLM
llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0,
    google_api_key=GOOGLE_API_KEY
)

# ✅ Agent instruction for DEX and LP fallback Mongo queries
query_instruction = SystemMessage(content="""
You are a MongoDB expert and DeFi data specialist.

You can access the following collections from the 'dex-temp-db':

1. swaps:
   - Fields: account_id, amountInUSD (string), amountOutUSD (string), timestamp (string - UNIX)
   - MUST use `$addFields` to cast `amountInUSD`, `amountOutUSD` to double, and `timestamp` to long

2. deposits:
   - Fields: account_id, amountInUSD (string), tokenIn.symbol, tokenOut.symbol, pool.name, timestamp (string - UNIX)
   - MUST cast `amountInUSD` to double, and `timestamp` to long

3. withdraws:
   - Fields: account_id, amountUSD (string), timestamp (string - UNIX)
   - MUST cast `amountUSD` to double, and `timestamp` to long

4. Z-Score:
   - Fields: wallet_id (string), aggregated_lp_score (int), lp_scores.total_deposit_all_time (double)

5. success:
   - Fields:
     - wallet_address (string)
     - protocolScores[]:
       - name (e.g., "aave", "compound_v3")
       - user_out[] (array of behavior metrics):
         - borrow (int)
         - repay (int)
         - liquidation (int)
         - supply (int)
         - average_time_to_first_repay (float)
         - average_time_between_borrows (float)
         - average_time_between_deposits (float)
         - average_time_between_liquidations (float)
         - borrow_mix(%volatile) (float)
         - deposit_mix(%volatile) (float)
   - Use only fields inside `user_out` for behavior analysis.
   - DO NOT use fields like from anywhere other than user_out use the actual field names exactly as listed.


⚠️ DO NOT use `ISODate(...)`. Use ISO date strings like "2021-01-01T00:00:00Z" if needed.

Return your output in this strict JSON format:
{
  "collection": "<collection_name>",
  "pipeline": [ ... ]
}

Only output valid JSON. No explanation.
""")

# ✅ Create reusable agent instance
query_agent = initialize_agent(
    tools=[MongoQueryTool],
    llm=llm,
    agent=AgentType.OPENAI_FUNCTIONS,
    verbose=True,
    agent_kwargs={"system_message": query_instruction}
)
