import os
import requests
from dotenv import load_dotenv
from config.settings import GEMINI_API_KEY

load_dotenv()
API_KEY = GEMINI_API_KEY
MODEL = "gemini-1.5-flash"
URL = f"https://generativelanguage.googleapis.com/v1beta/models/{MODEL}:generateContent?key={API_KEY}"

def generate_llm_prompt(wallet, score, memory, use_case, tier=None, terms_or_reason=None):
    is_group = isinstance(terms_or_reason, dict) and terms_or_reason.get("reason") and use_case.startswith("dex_group")

    subject = "group’s" if is_group else "wallet's"
    subject_may_be = "these wallets may be" if is_group else "this wallet may be"

    prompt = f"""You are a DeFi analytics expert analyzing {'a group of wallets' if is_group else 'wallet activity'} for: {use_case}.

Wallet {'Group' if is_group else 'Address'}: {wallet}
Wallet Score: {score}

"""

    # ✅ Include top wallet addresses if available
    if is_group and isinstance(terms_or_reason, dict) and terms_or_reason.get("top_wallets"):
        wallet_lines = "\n".join([f"- {w}" for w in terms_or_reason["top_wallets"]])
        prompt += f"📌 Top Wallets Analyzed:\n{wallet_lines}\n\n"

    # Include summary stats
    if is_group and terms_or_reason.get("reason"):
        prompt += f"{terms_or_reason['reason']}\n\n"

    prompt += f"""## Semantic Memory ({'Shared Traits' if is_group else 'General Traits'})
{chr(10).join(memory.get('semantic', [])) or 'N/A'}

## Procedural Memory ({'Shared Behavior Patterns' if is_group else 'Behavior Patterns'})
{chr(10).join(memory.get('procedural', [])) or 'N/A'}

## Episodic Memory ({'Aggregated Events' if is_group else 'Historical Events'})
{chr(10).join([f"{e['date']}: {e['event']}" for e in memory.get('episodic', [])]) or 'N/A'}

Instructions:
1. Provide a concise interpretation of the {subject} DeFi behavior based on memory and activity.
2. Explain why {subject_may_be} considered top trader(s), depositor(s), or participant(s).
3. Keep tone professional, analytical, and brief (2–3 short sentences).
4. mention the volumes of data if available from the memory.
"""

    return prompt

def generate_group_prompt(wallets, memory, stats, use_case):
    prompt = f"""You are a DeFi analytics expert analyzing a group of wallets for: {use_case}.

Wallet Group: {', '.join(wallets)}
{stats}

## Semantic Memory (Shared Traits)
{chr(10).join(memory.get('semantic', [])) or 'N/A'}

## Procedural Memory (Shared Behavior Patterns)
{chr(10).join(memory.get('procedural', [])) or 'N/A'}

## Episodic Memory (Aggregated Events)
{chr(10).join([f"{e['date']}: {e['event']}" for e in memory.get('episodic', [])]) or 'N/A'}

Instructions:
1. Summarize this group’s behavior across DeFi actions like swapping, depositing, or withdrawing.
2. Mention average volumes and how these suggest trading sophistication or activity levels.
3. Write 2–3 analytical sentences, without exaggeration.
"""
    return prompt

def ask_llm(prompt):
    headers = { "Content-Type": "application/json" }
    body = {
        "contents": [
            {
                "parts": [{"text": prompt}]
            }
        ]
    }
    response = requests.post(URL, headers=headers, json=body)
    response.raise_for_status()
    return response.json()["candidates"][0]["content"]["parts"][0]["text"]
