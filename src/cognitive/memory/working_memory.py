"""
Working Memory Component for CoALA Architecture
Manages active context and current reasoning state
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
import json
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

@dataclass
class ConversationContext:
    """Current conversation context"""
    user_id: str
    session_id: str
    current_wallet: Optional[str] = None
    active_protocols: List[str] = field(default_factory=list)
    analysis_focus: Optional[str] = None  # 'risk', 'reputation', 'comparison', etc.
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)

class WorkingMemory:
    """
    Working Memory Manager for CoALA Architecture
    Maintains active context and temporary information for ongoing tasks
    """
    
    def __init__(self, max_items: int = 100):
        self.max_items = max_items
        self.items: Dict[str, Any] = {}
        self.conversation_context: Optional[ConversationContext] = None
        self.active_tasks: List[Dict[str, Any]] = []
        self.reasoning_state: Dict[str, Any] = {}
        
    def set_conversation_context(self, context: ConversationContext):
        """Set the current conversation context"""
        self.conversation_context = context
        self.store("conversation_context", context, importance=1.0)
    
    def store(self, key: str, value: Any, importance: float = 0.5, context: str = ""):
        """Store an item in working memory"""
        self.items[key] = {
            'value': value,
            'timestamp': datetime.now(),
            'importance': importance,
            'context': context
        }
        
        # Cleanup if memory is full
        if len(self.items) > self.max_items:
            self._cleanup_memory()
    
    def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve an item from working memory"""
        item = self.items.get(key)
        return item['value'] if item else None
    
    def update_context_from_query(self, query: str):
        """Update context based on user query"""
        if not self.conversation_context:
            return
            
        entities = self.extract_entities(query)
        
        # Update current wallet
        if entities["wallets"]:
            self.conversation_context.current_wallet = entities["wallets"][0]
        
        # Update active protocols
        if entities["protocols"]:
            self.conversation_context.active_protocols.extend(entities["protocols"])
            # Remove duplicates
            self.conversation_context.active_protocols = list(set(
                self.conversation_context.active_protocols
            ))
        
        # Determine analysis focus
        focus_keywords = {
            'risk': ['risk', 'dangerous', 'safe', 'security', 'risky'],
            'reputation': ['reputation', 'score', 'rating', 'trustworthy', 'credible'],
            'comparison': ['compare', 'vs', 'versus', 'difference', 'better'],
            'analysis': ['analyze', 'breakdown', 'detailed', 'deep dive', 'examine']
        }
        
        for focus, keywords in focus_keywords.items():
            if any(keyword in query.lower() for keyword in keywords):
                self.conversation_context.analysis_focus = focus
                break
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract relevant entities from text"""
        entities = {
            "wallets": [],
            "protocols": [],
            "tokens": [],
            "amounts": []
        }
        
        import re
        
        # Extract wallet addresses
        wallet_pattern = r'0x[a-fA-F0-9]{40}'
        entities["wallets"] = re.findall(wallet_pattern, text)
        
        # Extract protocol names
        protocols = [
            'aave', 'compound', 'uniswap', 'sushiswap', 'curve', 'balancer', 'makerdao',
            'dydx', 'gmx', 'synthetix', 'yearn', 'convex', 'frax', 'lido',
            'pancakeswap', 'quickswap', 'spookyswap', 'dex', 'lending'
        ]
        for protocol in protocols:
            if protocol.lower() in text.lower():
                entities["protocols"].append(protocol)
        
        # Extract token symbols
        token_pattern = r'\b[A-Z]{2,6}\b'
        potential_tokens = re.findall(token_pattern, text.upper())
        common_tokens = ['ETH', 'BTC', 'USDC', 'USDT', 'DAI', 'WETH', 'WBTC', 'UNI', 'AAVE', 'COMP']
        entities["tokens"] = [token for token in potential_tokens if token in common_tokens]
        
        # Extract amounts
        amount_pattern = r'\$[\d,]+(?:\.\d{2})?'
        entities["amounts"] = re.findall(amount_pattern, text)
        
        return entities
    
    def add_conversation_turn(self, user_input: str, agent_response: str, metadata: Dict[str, Any] = None):
        """Add a conversation turn to history"""
        if not self.conversation_context:
            return
        
        turn = {
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "agent_response": agent_response,
            "metadata": metadata or {}
        }
        
        self.conversation_context.conversation_history.append(turn)
        
        # Keep only last 20 turns
        if len(self.conversation_context.conversation_history) > 20:
            self.conversation_context.conversation_history = \
                self.conversation_context.conversation_history[-20:]
    
    def get_context_summary(self) -> Dict[str, Any]:
        """Get a summary of current context for LLM"""
        summary = {
            "conversation_context": {},
            "active_tasks": len(self.active_tasks),
            "reasoning_state": self.reasoning_state,
            "key_items": []
        }
        
        if self.conversation_context:
            summary["conversation_context"] = {
                "current_wallet": self.conversation_context.current_wallet,
                "active_protocols": self.conversation_context.active_protocols,
                "analysis_focus": self.conversation_context.analysis_focus,
                "conversation_length": len(self.conversation_context.conversation_history)
            }
        
        return summary
    
    def _cleanup_memory(self):
        """Remove least important items when memory is full"""
        # Sort by importance and age
        items_by_priority = sorted(
            self.items.items(),
            key=lambda x: (x[1]['importance'], x[1]['timestamp']),
            reverse=True
        )
        
        # Keep only the most important items
        keep_items = items_by_priority[:self.max_items - 10]
        self.items = dict(keep_items)
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        return {
            "total_items": len(self.items),
            "max_items": self.max_items,
            "active_tasks": len([t for t in self.active_tasks if t.get('status') == 'active']),
            "conversation_turns": len(self.conversation_context.conversation_history) if self.conversation_context else 0,
            "memory_usage": f"{len(self.items)}/{self.max_items}"
        }

# Global working memory instance
working_memory = WorkingMemory()
