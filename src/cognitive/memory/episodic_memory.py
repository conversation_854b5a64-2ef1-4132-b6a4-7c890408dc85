"""
Episodic Memory Component for CoALA Architecture
Stores specific events and experiences for learning
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import hashlib
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

@dataclass
class Episode:
    """Individual episodic memory entry"""
    id: str
    timestamp: datetime
    event_type: str  # 'wallet_analysis', 'user_query', 'successful_insight', etc.
    entities: Dict[str, Any]  # wallets, protocols, tokens involved
    context: Dict[str, Any]  # situational context
    outcome: Dict[str, Any]  # results, insights, success/failure
    importance: float = 0.5  # 0-1 scale
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat(),
            'event_type': self.event_type,
            'entities': self.entities,
            'context': self.context,
            'outcome': self.outcome,
            'importance': self.importance
        }

class EpisodicMemory:
    """
    Episodic Memory Manager for CoALA Architecture
    Stores and retrieves specific events for learning and context
    """
    
    def __init__(self, max_episodes: int = 5000):
        self.max_episodes = max_episodes
        self.episodes: Dict[str, Episode] = {}
        self.indices = {
            'by_wallet': {},
            'by_protocol': {},
            'by_event_type': {}
        }
    
    def store_episode(self, 
                     event_type: str,
                     entities: Dict[str, Any],
                     context: Dict[str, Any],
                     outcome: Dict[str, Any],
                     importance: float = 0.5) -> str:
        """Store a new episode in memory"""
        
        # Generate unique ID
        episode_id = self._generate_episode_id(event_type, entities, context)
        
        episode = Episode(
            id=episode_id,
            timestamp=datetime.now(),
            event_type=event_type,
            entities=entities,
            context=context,
            outcome=outcome,
            importance=importance
        )
        
        self.episodes[episode_id] = episode
        self._update_indices(episode)
        
        # Cleanup if needed
        if len(self.episodes) > self.max_episodes:
            self._cleanup_episodes()
        
        return episode_id
    
    def get_wallet_history(self, wallet: str, limit: int = 50) -> List[Episode]:
        """Get history for a specific wallet"""
        wallet_episodes = self.indices['by_wallet'].get(wallet, set())
        episodes = [self.episodes[ep_id] for ep_id in wallet_episodes if ep_id in self.episodes]
        episodes.sort(key=lambda x: x.timestamp, reverse=True)
        return episodes[:limit]
    
    def get_protocol_patterns(self, protocol: str, days: int = 30) -> Dict[str, Any]:
        """Analyze patterns for a specific protocol"""
        since = datetime.now() - timedelta(days=days)
        protocol_episodes = self.indices['by_protocol'].get(protocol, set())
        
        recent_episodes = [
            self.episodes[ep_id] for ep_id in protocol_episodes 
            if ep_id in self.episodes and self.episodes[ep_id].timestamp >= since
        ]
        
        patterns = {
            'total_episodes': len(recent_episodes),
            'event_types': {},
            'success_rate': 0,
            'common_outcomes': []
        }
        
        if not recent_episodes:
            return patterns
        
        # Analyze event types
        for episode in recent_episodes:
            event_type = episode.event_type
            patterns['event_types'][event_type] = patterns['event_types'].get(event_type, 0) + 1
        
        # Calculate success rate
        successful = sum(1 for ep in recent_episodes if ep.outcome.get('success', False))
        patterns['success_rate'] = successful / len(recent_episodes) if recent_episodes else 0
        
        return patterns
    
    def find_similar_episodes(self, current_context: Dict[str, Any], limit: int = 10) -> List[Episode]:
        """Find episodes similar to current context"""
        scored_episodes = []
        
        for episode in self.episodes.values():
            similarity_score = self._calculate_similarity(current_context, episode)
            if similarity_score > 0.3:  # Threshold for relevance
                scored_episodes.append((episode, similarity_score))
        
        # Sort by similarity score
        scored_episodes.sort(key=lambda x: x[1], reverse=True)
        return [episode for episode, score in scored_episodes[:limit]]
    
    def get_learning_insights(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract learning insights from historical episodes"""
        similar_episodes = self.find_similar_episodes(context)
        
        insights = {
            'historical_precedents': len(similar_episodes),
            'success_patterns': [],
            'recommendations': [],
            'confidence': 0.0
        }
        
        if not similar_episodes:
            return insights
        
        # Analyze success patterns
        successful_episodes = [ep for ep in similar_episodes if ep.outcome.get('success', True)]
        
        if successful_episodes:
            insights['recommendations'].append(
                "Based on historical data, similar contexts have shown positive outcomes"
            )
        
        # Calculate confidence based on number of similar episodes
        insights['confidence'] = min(1.0, len(similar_episodes) / 10)
        
        return insights
    
    def _generate_episode_id(self, event_type: str, entities: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Generate unique episode ID"""
        content = f"{event_type}_{entities}_{context}_{datetime.now().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _update_indices(self, episode: Episode):
        """Update search indices"""
        episode_id = episode.id
        
        # Index by wallet
        wallets = episode.entities.get('wallets', [])
        if isinstance(wallets, str):
            wallets = [wallets]
        for wallet in wallets:
            if wallet not in self.indices['by_wallet']:
                self.indices['by_wallet'][wallet] = set()
            self.indices['by_wallet'][wallet].add(episode_id)
        
        # Index by protocol
        protocols = episode.entities.get('protocols', [])
        if isinstance(protocols, str):
            protocols = [protocols]
        for protocol in protocols:
            if protocol not in self.indices['by_protocol']:
                self.indices['by_protocol'][protocol] = set()
            self.indices['by_protocol'][protocol].add(episode_id)
        
        # Index by event type
        if episode.event_type not in self.indices['by_event_type']:
            self.indices['by_event_type'][episode.event_type] = set()
        self.indices['by_event_type'][episode.event_type].add(episode_id)
    
    def _calculate_similarity(self, context: Dict[str, Any], episode: Episode) -> float:
        """Calculate similarity between current context and episode"""
        score = 0.0
        total_factors = 0
        
        # Compare entities
        for entity_type in ['wallets', 'protocols', 'tokens']:
            if entity_type in context and entity_type in episode.entities:
                context_entities = set(context[entity_type]) if isinstance(context[entity_type], list) else {context[entity_type]}
                episode_entities = set(episode.entities[entity_type]) if isinstance(episode.entities[entity_type], list) else {episode.entities[entity_type]}
                
                if context_entities and episode_entities:
                    overlap = len(context_entities.intersection(episode_entities))
                    union = len(context_entities.union(episode_entities))
                    score += overlap / union if union > 0 else 0
                    total_factors += 1
        
        return score / total_factors if total_factors > 0 else 0.0
    
    def _cleanup_episodes(self):
        """Remove least important episodes when memory is full"""
        # Sort by importance and age
        episodes_by_priority = sorted(
            self.episodes.values(),
            key=lambda x: (x.importance, x.timestamp),
            reverse=True
        )
        
        # Keep only the most important episodes
        keep_episodes = episodes_by_priority[:self.max_episodes - 500]
        
        # Rebuild episodes dict and indices
        self.episodes = {ep.id: ep for ep in keep_episodes}
        self._rebuild_indices()
    
    def _rebuild_indices(self):
        """Rebuild all search indices"""
        self.indices = {
            'by_wallet': {},
            'by_protocol': {},
            'by_event_type': {}
        }
        
        for episode in self.episodes.values():
            self._update_indices(episode)

# Global episodic memory instance
episodic_memory = EpisodicMemory()
