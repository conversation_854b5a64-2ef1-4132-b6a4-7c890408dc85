"""
Base Plugin Interface for Modular Web3 Protocol Handlers
Provides foundation for hot-swappable protocol modules
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import logging
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

logger = logging.getLogger(__name__)

class PluginStatus(Enum):
    """Plugin status states"""
    INACTIVE = "inactive"
    LOADING = "loading"
    ACTIVE = "active"
    ERROR = "error"

@dataclass
class AnalysisResult:
    """Standardized analysis result from plugins"""
    wallet_address: str
    protocol: str
    analysis_type: str
    scores: Dict[str, float]
    insights: List[str]
    recommendations: List[str]
    risk_factors: List[str]
    confidence: float = 0.0
    data_quality: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'wallet_address': self.wallet_address,
            'protocol': self.protocol,
            'analysis_type': self.analysis_type,
            'scores': self.scores,
            'insights': self.insights,
            'recommendations': self.recommendations,
            'risk_factors': self.risk_factors,
            'confidence': self.confidence,
            'data_quality': self.data_quality,
            'metadata': self.metadata
        }

class BaseProtocolPlugin(ABC):
    """
    Abstract base class for all protocol plugins
    Each protocol implements this interface for modular functionality
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.status = PluginStatus.INACTIVE
        self.name = self.__class__.__name__
        self.error_message: Optional[str] = None
        
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the plugin"""
        pass
    
    @abstractmethod
    async def analyze_wallet(self, wallet_address: str, analysis_type: str = "comprehensive") -> AnalysisResult:
        """Analyze a wallet for this protocol"""
        pass
    
    @abstractmethod
    async def handle_query(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle natural language queries specific to this protocol"""
        pass
    
    @abstractmethod
    def get_supported_queries(self) -> List[str]:
        """Get example queries this plugin can handle"""
        pass
    
    async def health_check(self) -> Dict[str, Any]:
        """Check plugin health"""
        return {
            'status': self.status.value,
            'error': self.error_message,
            'name': self.name
        }
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self.config.get(key, default)
    
    async def start(self) -> bool:
        """Start the plugin"""
        try:
            self.status = PluginStatus.LOADING
            success = await self.initialize()
            
            if success:
                self.status = PluginStatus.ACTIVE
                logger.info(f"Plugin {self.name} started successfully")
                return True
            else:
                self.status = PluginStatus.ERROR
                self.error_message = "Initialization failed"
                return False
                
        except Exception as e:
            self.status = PluginStatus.ERROR
            self.error_message = str(e)
            logger.error(f"Plugin {self.name} failed to start: {str(e)}")
            return False
    
    def is_active(self) -> bool:
        """Check if plugin is active"""
        return self.status == PluginStatus.ACTIVE
    
    def get_info(self) -> Dict[str, Any]:
        """Get plugin information"""
        return {
            'name': self.name,
            'status': self.status.value,
            'error': self.error_message,
            'config': self.config
        }

class PluginManager:
    """Plugin Manager for handling protocol plugins"""
    
    def __init__(self):
        self.plugins: Dict[str, BaseProtocolPlugin] = {}
        
    def register_plugin(self, plugin: BaseProtocolPlugin, name: str = None):
        """Register a plugin"""
        plugin_name = name or plugin.name
        self.plugins[plugin_name] = plugin
        logger.info(f"Registered plugin: {plugin_name}")
    
    async def start_plugin(self, name: str) -> bool:
        """Start a specific plugin"""
        if name not in self.plugins:
            logger.error(f"Plugin {name} not found")
            return False
        
        plugin = self.plugins[name]
        return await plugin.start()
    
    async def start_all_plugins(self) -> Dict[str, bool]:
        """Start all registered plugins"""
        results = {}
        for name, plugin in self.plugins.items():
            results[name] = await plugin.start()
        return results
    
    def get_active_plugins(self) -> List[str]:
        """Get list of active plugin names"""
        return [name for name, plugin in self.plugins.items() if plugin.is_active()]
    
    def get_plugin(self, name: str) -> Optional[BaseProtocolPlugin]:
        """Get a specific plugin"""
        return self.plugins.get(name)
    
    async def analyze_wallet_all_plugins(self, wallet_address: str) -> Dict[str, AnalysisResult]:
        """Analyze wallet with all active plugins"""
        results = {}
        
        for name, plugin in self.plugins.items():
            if plugin.is_active():
                try:
                    result = await plugin.analyze_wallet(wallet_address)
                    results[name] = result
                except Exception as e:
                    logger.error(f"Plugin {name} analysis failed: {str(e)}")
        
        return results
    
    async def query_plugins(self, query: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Query all active plugins with a natural language query"""
        results = []
        
        for name, plugin in self.plugins.items():
            if plugin.is_active():
                try:
                    result = await plugin.handle_query(query, context)
                    if result:
                        result['plugin'] = name
                        results.append(result)
                except Exception as e:
                    logger.error(f"Plugin {name} query failed: {str(e)}")
        
        return results
    
    def get_all_supported_queries(self) -> Dict[str, List[str]]:
        """Get all supported queries from all plugins"""
        queries = {}
        for name, plugin in self.plugins.items():
            if plugin.is_active():
                queries[name] = plugin.get_supported_queries()
        return queries
    
    async def health_check_all(self) -> Dict[str, Dict[str, Any]]:
        """Run health check on all plugins"""
        results = {}
        for name, plugin in self.plugins.items():
            results[name] = await plugin.health_check()
        return results
    
    def get_manager_info(self) -> Dict[str, Any]:
        """Get plugin manager information"""
        return {
            'total_plugins': len(self.plugins),
            'active_plugins': len(self.get_active_plugins()),
            'plugins': {name: plugin.get_info() for name, plugin in self.plugins.items()}
        }

# Global plugin manager instance
plugin_manager = PluginManager()
