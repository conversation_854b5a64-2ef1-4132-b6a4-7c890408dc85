"""
Enhanced DEX Plugin with CoALA Integration
Uses your existing MongoDB queries while adding memory and learning
"""

from typing import Dict, List, Any, Optional
import json
import logging
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.plugins.base_plugin import BaseProtocolPlugin, AnalysisResult
from src.cognitive.memory.working_memory import working_memory
from src.cognitive.memory.episodic_memory import episodic_memory
from src.database.mongo_client import run_custom_mongo_query

logger = logging.getLogger(__name__)

class EnhancedDEXPlugin(BaseProtocolPlugin):
    """
    Enhanced DEX Plugin with CoALA cognitive architecture integration
    
    Uses your existing MongoDB scoring system while adding:
    - Memory-based learning
    - Contextual analysis
    - Behavioral pattern recognition
    - Improved insights generation
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.name = "enhanced_dex"
        self.supported_dexes = self.get_config_value('supported_dexes', [
            'uniswap', 'sushiswap', '1inch', 'curve', 'balancer', 'pancakeswap'
        ])
        
    async def initialize(self) -> bool:
        """Initialize the plugin"""
        try:
            # Test database connection using your existing system
            test_result = run_custom_mongo_query("swaps", [{"$limit": 1}])
            if test_result and not test_result.startswith("Error"):
                logger.info("Enhanced DEX plugin initialized successfully")
                return True
            else:
                logger.error("Failed to connect to DEX data")
                return False
        except Exception as e:
            logger.error(f"DEX plugin initialization failed: {str(e)}")
            return False
    
    async def analyze_wallet(self, wallet_address: str, analysis_type: str = "comprehensive") -> AnalysisResult:
        """
        Comprehensive wallet analysis with memory integration
        Uses your existing MongoDB queries but adds cognitive enhancements
        """
        try:
            # Check working memory for recent analysis
            cache_key = f"dex_analysis_{wallet_address}_{analysis_type}"
            cached_result = working_memory.retrieve(cache_key)
            
            if cached_result:
                logger.info(f"Using cached DEX analysis for {wallet_address}")
                return cached_result
            
            # Get historical context from episodic memory
            historical_episodes = episodic_memory.get_wallet_history(wallet_address, limit=5)
            
            # Perform core analysis using your existing MongoDB queries
            trading_metrics = await self._get_trading_metrics(wallet_address)
            behavioral_insights = await self._analyze_behavior_with_memory(wallet_address, historical_episodes)
            
            # Create analysis result
            result = AnalysisResult(
                wallet_address=wallet_address,
                protocol="dex",
                analysis_type=analysis_type,
                scores={},
                insights=[],
                recommendations=[],
                risk_factors=[]
            )
            
            # Calculate scores from your existing data
            if trading_metrics.get('data_complete', False):
                result.scores = {
                    "trading_volume": min(100, trading_metrics.get('total_volume', 0) / 10000),
                    "trading_frequency": min(100, trading_metrics.get('trade_count', 0) * 0.5),
                    "token_diversity": min(100, len(trading_metrics.get('unique_tokens', [])) * 5),
                    "consistency": self._calculate_consistency_score(trading_metrics)
                }
                
                # Generate insights
                result.insights.extend(self._generate_enhanced_insights(trading_metrics, behavioral_insights))
                
                # Generate recommendations
                result.recommendations.extend(self._generate_smart_recommendations(trading_metrics, historical_episodes))
                
                # Assess risk factors
                result.risk_factors.extend(self._assess_enhanced_risks(trading_metrics))
                
                # Set confidence based on data quality
                result.confidence = min(1.0, trading_metrics.get('trade_count', 0) / 100)
                result.data_quality = 0.9
            else:
                result.insights.append("Limited trading data available for comprehensive analysis")
                result.confidence = 0.1
                result.data_quality = 0.3
            
            # Store in working memory for future use
            working_memory.store(cache_key, result, importance=0.7)
            
            # Store episode in episodic memory for learning
            episodic_memory.store_episode(
                event_type="wallet_analysis",
                entities={"wallets": [wallet_address], "protocols": ["dex"]},
                context={"analysis_type": analysis_type, "plugin": "enhanced_dex"},
                outcome={"success": True, "scores": result.scores, "insights_count": len(result.insights)},
                importance=0.6
            )
            
            return result
            
        except Exception as e:
            logger.error(f"DEX analysis failed for {wallet_address}: {str(e)}")
            return AnalysisResult(
                wallet_address=wallet_address,
                protocol="dex",
                analysis_type=analysis_type,
                scores={},
                insights=[f"Analysis failed: {str(e)}"],
                recommendations=[],
                risk_factors=[],
                confidence=0.0
            )
    
    async def _get_trading_metrics(self, wallet_address: str) -> Dict[str, Any]:
        """Get comprehensive trading metrics using your existing MongoDB queries"""
        
        # Your existing aggregation pipeline
        pipeline = [
            {"$match": {"account_id": wallet_address}},
            {"$addFields": {
                "amountInUSD": {"$toDouble": "$amountInUSD"},
                "amountOutUSD": {"$toDouble": "$amountOutUSD"},
                "timestamp": {"$toLong": "$timestamp"}
            }},
            {"$group": {
                "_id": "$account_id",
                "total_volume": {"$sum": {"$add": ["$amountInUSD", "$amountOutUSD"]}},
                "trade_count": {"$sum": 1},
                "avg_trade_size": {"$avg": {"$add": ["$amountInUSD", "$amountOutUSD"]}},
                "max_trade_size": {"$max": {"$add": ["$amountInUSD", "$amountOutUSD"]}},
                "unique_tokens": {"$addToSet": "$inputTokens.symbol"},
                "first_trade": {"$min": "$timestamp"},
                "last_trade": {"$max": "$timestamp"},
                "protocols": {"$addToSet": "$protocol"}
            }}
        ]
        
        try:
            result = run_custom_mongo_query("swaps", pipeline)
            if result and not result.startswith("Error"):
                data = json.loads(result)
                if data:
                    metrics = data[0]
                    metrics['data_complete'] = True
                    return metrics
            
            return {'data_complete': False}
            
        except Exception as e:
            logger.error(f"Error getting trading metrics: {str(e)}")
            return {'data_complete': False, 'error': str(e)}
    
    async def _analyze_behavior_with_memory(self, wallet_address: str, historical_episodes: List) -> Dict[str, Any]:
        """Analyze behavioral patterns using memory"""
        
        insights = {
            'pattern_recognition': [],
            'learning_insights': [],
            'behavioral_score': 50
        }
        
        # Analyze historical episodes for patterns
        if historical_episodes:
            insights['pattern_recognition'].append(f"Found {len(historical_episodes)} previous interactions")
            
            # Look for improvement patterns
            for episode in historical_episodes:
                if episode.event_type == "wallet_analysis":
                    prev_scores = episode.outcome.get('scores', {})
                    if prev_scores.get('trading_volume', 0) > 50:
                        insights['behavioral_score'] += 10
                        insights['learning_insights'].append("Consistently high trading volume indicates active user")
        
        return insights
    
    def _calculate_consistency_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate trading consistency score"""
        trade_count = metrics.get('trade_count', 0)
        if trade_count < 2:
            return 0
        
        first_trade = metrics.get('first_trade', 0)
        last_trade = metrics.get('last_trade', 0)
        
        if first_trade and last_trade:
            days_active = (last_trade - first_trade) / (24 * 60 * 60)
            if days_active > 0:
                trades_per_day = trade_count / days_active
                return min(100, trades_per_day * 10)
        
        return 25
    
    def _generate_enhanced_insights(self, trading_metrics: Dict[str, Any], behavioral_insights: Dict[str, Any]) -> List[str]:
        """Generate enhanced insights using both data and memory"""
        insights = []
        
        volume = trading_metrics.get('total_volume', 0)
        trade_count = trading_metrics.get('trade_count', 0)
        unique_tokens = len(trading_metrics.get('unique_tokens', []))
        
        # Volume-based insights
        if volume > 100000:
            insights.append(f"High-volume trader with ${volume:,.0f} total trading volume")
        elif volume > 10000:
            insights.append(f"Active trader with ${volume:,.0f} total trading volume")
        
        # Frequency insights
        if trade_count > 1000:
            insights.append(f"Very active with {trade_count} total transactions")
        elif trade_count > 100:
            insights.append(f"Regular trader with {trade_count} transactions")
        
        # Diversity insights
        if unique_tokens > 20:
            insights.append(f"Highly diversified portfolio with {unique_tokens} different tokens")
        elif unique_tokens > 10:
            insights.append(f"Well-diversified with {unique_tokens} different tokens")
        
        # Add behavioral insights
        insights.extend(behavioral_insights.get('learning_insights', []))
        
        return insights
    
    def _generate_smart_recommendations(self, trading_metrics: Dict[str, Any], historical_episodes: List) -> List[str]:
        """Generate smart recommendations based on data and memory"""
        recommendations = []
        
        volume = trading_metrics.get('total_volume', 0)
        unique_tokens = len(trading_metrics.get('unique_tokens', []))
        
        if volume < 10000:
            recommendations.append("Consider increasing trading volume to improve reputation score")
        
        if unique_tokens < 5:
            recommendations.append("Diversify token portfolio to reduce concentration risk")
        
        # Memory-based recommendations
        if historical_episodes:
            recommendations.append("Based on your trading history, consider exploring new DeFi protocols")
        
        return recommendations
    
    def _assess_enhanced_risks(self, trading_metrics: Dict[str, Any]) -> List[str]:
        """Assess risk factors with enhanced analysis"""
        risks = []
        
        volume = trading_metrics.get('total_volume', 0)
        max_trade = trading_metrics.get('max_trade_size', 0)
        
        if volume > 1000000:
            risks.append("Very high trading volume may indicate excessive risk exposure")
        
        if max_trade > volume * 0.5:
            risks.append("Large single trades indicate potential concentration risk")
        
        return risks
    
    async def handle_query(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle natural language queries"""
        query_lower = query.lower()
        
        # Extract wallet address if present
        import re
        wallet_pattern = r'0x[a-fA-F0-9]{40}'
        wallets = re.findall(wallet_pattern, query)
        
        if wallets:
            wallet = wallets[0]
            
            if any(keyword in query_lower for keyword in ['analyze', 'reputation', 'score', 'trading']):
                analysis = await self.analyze_wallet(wallet)
                return {
                    'type': 'wallet_analysis',
                    'wallet': wallet,
                    'analysis': analysis.to_dict(),
                    'response': f"DEX analysis for {wallet[:10]}...: {analysis.insights[0] if analysis.insights else 'Analysis completed'}"
                }
        
        # Handle general queries
        if any(keyword in query_lower for keyword in ['top traders', 'leaderboard']):
            return {
                'type': 'general_info',
                'response': 'Top DEX traders analysis requires leaderboard implementation with your scoring system'
            }
        
        return {
            'type': 'unsupported',
            'response': 'This query is not supported by the DEX plugin. Try asking about a specific wallet address.'
        }
    
    def get_supported_queries(self) -> List[str]:
        """Get supported query examples"""
        return [
            "Analyze DEX trading for wallet 0x...",
            "What is the trading reputation of 0x...?",
            "Show me DEX activity patterns for 0x...",
            "Compare DEX performance of wallet 0x...",
            "What are the risk factors for DEX trading by 0x...?",
            "Analyze trading sophistication of 0x..."
        ]
