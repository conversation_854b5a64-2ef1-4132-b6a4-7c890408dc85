#!/bin/bash

# CORS Testing Script for DeFi Analytics Agent
# This script tests CORS configuration from different origins

echo "🧪 Testing CORS Configuration"
echo "============================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Test endpoints
LOCAL_ENDPOINT="http://localhost:8001"
HTTPS_ENDPOINT="https://agentapi.myzscore.ai"

# Test origins
ORIGINS=(
    "http://localhost:3000"
    "https://localhost:3000"
    "https://agent.myzscore.ai"
    "https://sentient-ui-alpha.vercel.app"
)

# Function to test CORS preflight
test_cors_preflight() {
    local endpoint=$1
    local origin=$2
    
    print_status "Testing CORS preflight for $origin -> $endpoint"
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Origin: $origin" \
        -H "Access-Control-Request-Method: POST" \
        -H "Access-Control-Request-Headers: Content-Type" \
        -X OPTIONS \
        "$endpoint/query" 2>/dev/null)
    
    http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "204" ]; then
        print_success "✅ CORS preflight successful (HTTP $http_code)"
    else
        print_error "❌ CORS preflight failed (HTTP $http_code)"
    fi
}

# Function to test actual request
test_cors_request() {
    local endpoint=$1
    local origin=$2
    
    print_status "Testing CORS actual request for $origin -> $endpoint"
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Origin: $origin" \
        -H "Content-Type: application/json" \
        -X POST \
        -d '{"prompt": "hello"}' \
        "$endpoint/query" 2>/dev/null)
    
    http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')
    
    if [ "$http_code" = "200" ]; then
        print_success "✅ CORS request successful (HTTP $http_code)"
        echo "   Response: ${body:0:100}..."
    else
        print_error "❌ CORS request failed (HTTP $http_code)"
        echo "   Response: $body"
    fi
}

# Test health endpoint (should always work)
print_status "Testing health endpoint..."
health_response=$(curl -s "$LOCAL_ENDPOINT/health" 2>/dev/null || echo "Failed")
if [[ "$health_response" == *"DEX Analyst Agent is running"* ]]; then
    print_success "✅ Health endpoint is working"
else
    print_error "❌ Health endpoint failed: $health_response"
fi

echo ""
print_status "Testing CORS with local endpoint ($LOCAL_ENDPOINT)..."
echo "=================================================="

for origin in "${ORIGINS[@]}"; do
    echo ""
    test_cors_preflight "$LOCAL_ENDPOINT" "$origin"
    test_cors_request "$LOCAL_ENDPOINT" "$origin"
done

echo ""
print_status "Testing CORS with HTTPS endpoint ($HTTPS_ENDPOINT)..."
echo "=================================================="

for origin in "${ORIGINS[@]}"; do
    echo ""
    test_cors_preflight "$HTTPS_ENDPOINT" "$origin"
    test_cors_request "$HTTPS_ENDPOINT" "$origin"
done

echo ""
print_success "CORS testing completed!"
echo ""
echo "📝 Notes:"
echo "  - If HTTPS tests fail, make sure the domain is properly configured"
echo "  - If local tests fail, check if the FastAPI app is running on port 8001"
echo "  - CORS preflight requests should return HTTP 200 or 204"
echo "  - Actual requests should return HTTP 200 with valid JSON response"
