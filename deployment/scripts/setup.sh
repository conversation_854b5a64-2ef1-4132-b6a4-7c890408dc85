#!/bin/bash

# DeFi Analytics Agent - Initial Setup Script
# Run this script only once to set up the environment

set -e  # Exit on any error

echo "🚀 DeFi Analytics Agent - Initial Setup"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3 is installed
print_status "Checking Python installation..."
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
print_success "Python $PYTHON_VERSION found"

# Check if pip is installed
print_status "Checking pip installation..."
if ! command -v pip3 &> /dev/null; then
    print_warning "pip3 is not installed. Installing pip3..."

    # Get Python version for version-specific packages
    PYTHON_MINOR_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)

    # Detect OS and install pip3 accordingly
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        print_status "Installing pip3 and python3-venv using apt-get..."
        sudo apt-get update
        sudo apt-get install -y python3-pip python3-venv python${PYTHON_MINOR_VERSION}-venv
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL/Fedora
        print_status "Installing pip3 using yum..."
        sudo yum install -y python3-pip python3-venv
    elif command -v dnf &> /dev/null; then
        # Fedora (newer versions)
        print_status "Installing pip3 using dnf..."
        sudo dnf install -y python3-pip python3-venv
    elif command -v brew &> /dev/null; then
        # macOS with Homebrew
        print_status "Installing pip3 using brew..."
        brew install python3
    else
        print_error "Could not detect package manager. Please install pip3 and python3-venv manually."
        print_error "On Ubuntu/Debian: sudo apt-get install python3-pip python3-venv"
        print_error "On CentOS/RHEL: sudo yum install python3-pip python3-venv"
        print_error "On Fedora: sudo dnf install python3-pip python3-venv"
        print_error "On macOS: brew install python3"
        exit 1
    fi

    # Verify pip3 installation
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 installation failed. Please install pip3 manually."
        exit 1
    fi

    print_success "pip3 installed successfully"
else
    print_success "pip3 found"
fi

# Check if python3-venv is available (needed for virtual environments)
print_status "Checking python3-venv availability..."

# Get Python version for version-specific venv package
PYTHON_MINOR_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)

if ! python3 -m venv --help &> /dev/null; then
    print_warning "python3-venv is not available. Installing python3-venv..."

    # Detect OS and install python3-venv accordingly
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian - install both generic and version-specific venv
        print_status "Installing python3-venv and python${PYTHON_MINOR_VERSION}-venv using apt-get..."
        sudo apt-get install -y python3-venv python${PYTHON_MINOR_VERSION}-venv
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL/Fedora
        print_status "Installing python3-venv using yum..."
        sudo yum install -y python3-venv
    elif command -v dnf &> /dev/null; then
        # Fedora (newer versions)
        print_status "Installing python3-venv using dnf..."
        sudo dnf install -y python3-venv
    else
        print_error "Could not detect package manager. Please install python3-venv manually."
        print_error "On Ubuntu/Debian: sudo apt-get install python3-venv python${PYTHON_MINOR_VERSION}-venv"
        print_error "On CentOS/RHEL: sudo yum install python3-venv"
        print_error "On Fedora: sudo dnf install python3-venv"
        exit 1
    fi

    # Verify python3-venv installation
    if ! python3 -m venv --help &> /dev/null; then
        print_error "python3-venv installation failed. Please install python3-venv manually."
        print_error "Try: sudo apt-get install python3-venv python${PYTHON_MINOR_VERSION}-venv"
        exit 1
    fi

    print_success "python3-venv installed successfully"
else
    print_success "python3-venv is available"
fi

# Create virtual environment
print_status "Creating virtual environment..."
if [ -d "venv" ]; then
    print_warning "Virtual environment already exists. Removing old one..."
    rm -rf venv
fi

python3 -m venv venv
print_success "Virtual environment created"

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate
print_success "Virtual environment activated"

# Upgrade pip
print_status "Upgrading pip..."
pip install --upgrade pip
print_success "pip upgraded"

# Install dependencies
print_status "Installing dependencies from requirements.txt..."
if [ ! -f "requirements.txt" ]; then
    print_error "requirements.txt not found!"
    exit 1
fi

pip install -r requirements.txt
print_success "Dependencies installed"

# Check if .env file exists
print_status "Checking environment configuration..."
if [ ! -f ".env" ]; then
    print_warning ".env file not found!"
    echo "Creating .env template..."
    cat > .env << EOF
# DeFi Analytics Agent Environment Variables
# Please fill in your actual API keys and configuration

# Google/Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# MongoDB Connection
MONGO_URI=your_mongodb_uri_here

# ZPass API Key
ZPASS_API_KEY=your_zpass_api_key_here

# Fireworks AI API Key
FIREWORKS_API_KEY=your_fireworks_api_key_here
EOF
    print_warning "Please edit .env file with your actual API keys before running the application!"
else
    print_success ".env file found"
fi

# Test imports
print_status "Testing module imports..."
python3 -c "
import sys
modules_to_test = ['fastapi', 'uvicorn', 'pymongo', 'python_dotenv', 'langchain', 'langchain_google_genai']
failed_imports = []

for module in modules_to_test:
    try:
        if module == 'python_dotenv':
            __import__('dotenv')
        else:
            __import__(module)
        print(f'✅ {module}')
    except ImportError as e:
        print(f'❌ {module}: {e}')
        failed_imports.append(module)

if failed_imports:
    print(f'Failed to import: {failed_imports}')
    sys.exit(1)
else:
    print('All required modules imported successfully!')
"

if [ $? -eq 0 ]; then
    print_success "All dependencies are working correctly"
else
    print_error "Some dependencies failed to import"
    exit 1
fi

# Test application modules
print_status "Testing application modules..."
python3 -c "
import sys
sys.path.append('.')

modules_to_test = ['agent', 'main', 'mongo_tool', 'llm_agent', 'query_parser', 'lp_usecases', 'llm_utils', 'db']
failed_imports = []

for module in modules_to_test:
    try:
        __import__(module)
        print(f'✅ {module}')
    except Exception as e:
        print(f'❌ {module}: {e}')
        failed_imports.append(module)

if failed_imports:
    print(f'Failed to import application modules: {failed_imports}')
    sys.exit(1)
else:
    print('All application modules imported successfully!')
"

if [ $? -eq 0 ]; then
    print_success "All application modules are working correctly"
else
    print_error "Some application modules failed to import"
    exit 1
fi

# Create run script
print_status "Creating run script..."
cat > run.sh << 'EOF'
#!/bin/bash

# DeFi Analytics Agent - Run Script
# Use this script to start the application after initial setup

set -e

echo "🚀 Starting DeFi Analytics Agent..."
echo "================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_error "Virtual environment not found! Please run setup.sh first."
    exit 1
fi

# Check if .env file exists and has content
if [ ! -f ".env" ]; then
    print_error ".env file not found! Please create it with your API keys."
    exit 1
fi

# Check if .env has placeholder values
if grep -q "your_.*_api_key_here\|your_.*_uri_here" .env; then
    print_error "Please update .env file with your actual API keys and configuration!"
    echo "Edit .env file and replace placeholder values with real ones."
    exit 1
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Check environment variables
print_status "Checking environment variables..."
python3 -c "
import os
from dotenv import load_dotenv
load_dotenv()

required_vars = ['GEMINI_API_KEY', 'GOOGLE_API_KEY', 'MONGO_URI', 'ZPASS_API_KEY', 'FIREWORKS_API_KEY']
missing_vars = []

for var in required_vars:
    if not os.getenv(var):
        missing_vars.append(var)
    else:
        print(f'✅ {var}: Set')

if missing_vars:
    print(f'❌ Missing environment variables: {missing_vars}')
    exit(1)
else:
    print('All environment variables are set!')
"

if [ $? -ne 0 ]; then
    print_error "Environment variables check failed!"
    exit 1
fi

print_success "Environment variables verified"

# Start the application
print_status "Starting FastAPI server..."
print_success "DeFi Analytics Agent is starting on http://localhost:8001"
echo ""
echo "Available endpoints:"
echo "  - Health check: GET  http://localhost:8001/health"
echo "  - Query:        POST http://localhost:8001/query"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start uvicorn server
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
EOF

chmod +x run.sh
print_success "Run script created (run.sh)"

# Final setup completion
echo ""
echo "🎉 Setup completed successfully!"
echo "================================"
echo ""
print_success "Next steps:"
echo "1. Edit .env file with your actual API keys"
echo "2. Run './run.sh' to start the application"
echo ""
print_status "Quick test commands:"
echo "  Health check: curl http://localhost:8001/health"
echo "  Test query:   curl -X POST http://localhost:8001/query -H 'Content-Type: application/json' -d '{\"prompt\": \"hello\"}'"
echo ""
print_warning "Remember to keep your .env file secure and never commit it to version control!"
