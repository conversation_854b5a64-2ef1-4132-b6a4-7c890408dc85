#!/bin/bash

# DeFi Analytics Agent - Run Script
# Use this script to start the application after initial setup

set -e

echo "🚀 Starting DeFi Analytics Agent..."
echo "================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_error "Virtual environment not found! Please run setup.sh first."
    exit 1
fi

# Check if .env file exists and has content
if [ ! -f ".env" ]; then
    print_error ".env file not found! Please create it with your API keys."
    exit 1
fi

# Check if .env has placeholder values
if grep -q "your_.*_api_key_here\|your_.*_uri_here" .env; then
    print_error "Please update .env file with your actual API keys and configuration!"
    echo "Edit .env file and replace placeholder values with real ones."
    exit 1
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Check environment variables
print_status "Checking environment variables..."
python3 -c "
import os
from dotenv import load_dotenv
load_dotenv()

required_vars = ['GEMINI_API_KEY', 'GOOGLE_API_KEY', 'MONGO_URI', 'ZPASS_API_KEY', 'FIREWORKS_API_KEY']
missing_vars = []

for var in required_vars:
    if not os.getenv(var):
        missing_vars.append(var)
    else:
        print(f'✅ {var}: Set')

if missing_vars:
    print(f'❌ Missing environment variables: {missing_vars}')
    exit(1)
else:
    print('All environment variables are set!')
"

if [ $? -ne 0 ]; then
    print_error "Environment variables check failed!"
    exit 1
fi

print_success "Environment variables verified"

# Start the application
print_status "Starting FastAPI server..."
print_success "DeFi Analytics Agent is starting on http://localhost:8001"
echo ""
echo "Available endpoints:"
echo "  - Health check: GET  http://localhost:8001/health"
echo "  - Query:        POST http://localhost:8001/query"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start uvicorn server
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
