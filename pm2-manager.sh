#!/bin/bash

# DeFi Analytics Agent - PM2 Management Script
# Comprehensive PM2 management with interactive menu

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# App configuration
APP_NAME="defi-analytics-agent"
APP_SCRIPT="main.py"
APP_PORT=8001

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_menu_item() {
    echo -e "${CYAN}$1${NC} $2"
}

# Check if Node.js is installed
check_nodejs() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed!"
        echo "Please install Node.js first:"
        echo "  - macOS: brew install node"
        echo "  - Ubuntu: sudo apt install nodejs npm"
        echo "  - Or visit: https://nodejs.org/"
        exit 1
    fi
    print_success "Node.js $(node --version) found"
}

# Check if npm is installed
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed!"
        exit 1
    fi
    print_success "npm $(npm --version) found"
}

# Install PM2 globally
install_pm2() {
    print_status "Installing PM2 globally..."
    if command -v pm2 &> /dev/null; then
        print_warning "PM2 is already installed ($(pm2 --version))"
        return 0
    fi
    
    sudo npm install -g pm2
    print_success "PM2 installed successfully"
}

# Check if virtual environment and dependencies are ready
check_setup() {
    if [ ! -d "venv" ]; then
        print_error "Virtual environment not found! Please run setup.sh first."
        return 1
    fi
    
    if [ ! -f ".env" ]; then
        print_error ".env file not found! Please create it with your API keys."
        return 1
    fi
    
    if grep -q "your_.*_api_key_here\|your_.*_uri_here" .env; then
        print_error "Please update .env file with your actual API keys!"
        return 1
    fi
    
    print_success "Setup verification passed"
    return 0
}

# Create PM2 ecosystem file
create_ecosystem_file() {
    print_status "Creating PM2 ecosystem file..."
    
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '${APP_NAME}',
    script: 'uvicorn',
    args: 'main:app --host 0.0.0.0 --port ${APP_PORT}',
    cwd: '$(pwd)',
    interpreter: '$(pwd)/venv/bin/python',
    env: {
      NODE_ENV: 'production',
      PORT: ${APP_PORT}
    },
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    restart_delay: 4000
  }]
};
EOF
    
    # Create logs directory
    mkdir -p logs
    
    print_success "PM2 ecosystem file created"
}

# Start the application with PM2
start_app() {
    print_status "Starting ${APP_NAME} with PM2..."
    
    if ! check_setup; then
        return 1
    fi
    
    create_ecosystem_file
    
    # Check if app is already running
    if pm2 list | grep -q "${APP_NAME}"; then
        print_warning "Application is already running. Use restart option instead."
        return 1
    fi
    
    pm2 start ecosystem.config.js
    pm2 save
    
    print_success "Application started successfully!"
    show_app_info
}

# Stop the application
stop_app() {
    print_status "Stopping ${APP_NAME}..."
    
    if ! pm2 list | grep -q "${APP_NAME}"; then
        print_warning "Application is not running"
        return 1
    fi
    
    pm2 stop ${APP_NAME}
    print_success "Application stopped"
}

# Restart the application
restart_app() {
    print_status "Restarting ${APP_NAME}..."
    
    if ! check_setup; then
        return 1
    fi
    
    if ! pm2 list | grep -q "${APP_NAME}"; then
        print_warning "Application is not running. Starting instead..."
        start_app
        return 0
    fi
    
    create_ecosystem_file
    pm2 restart ${APP_NAME}
    
    print_success "Application restarted successfully!"
    show_app_info
}

# Delete the application from PM2
delete_app() {
    print_status "Deleting ${APP_NAME} from PM2..."
    
    if ! pm2 list | grep -q "${APP_NAME}"; then
        print_warning "Application is not running"
        return 1
    fi
    
    pm2 delete ${APP_NAME}
    print_success "Application deleted from PM2"
}

# Show application status and info
show_status() {
    print_header "PM2 Application Status"
    pm2 list
    echo ""
    
    if pm2 list | grep -q "${APP_NAME}"; then
        print_header "Application Details"
        pm2 show ${APP_NAME}
    fi
}

# Show application logs
show_logs() {
    print_status "Showing logs for ${APP_NAME}..."
    if ! pm2 list | grep -q "${APP_NAME}"; then
        print_warning "Application is not running"
        return 1
    fi
    
    echo "Press Ctrl+C to exit log view"
    pm2 logs ${APP_NAME} --lines 50
}

# Monitor application
monitor_app() {
    print_status "Opening PM2 monitor..."
    pm2 monit
}

# Show application info
show_app_info() {
    echo ""
    print_success "🚀 DeFi Analytics Agent is running!"
    echo ""
    echo "📡 Available endpoints:"
    echo "  - Health check: GET  http://localhost:${APP_PORT}/health"
    echo "  - Query:        POST http://localhost:${APP_PORT}/query"
    echo ""
    echo "🧪 Test commands:"
    echo "  Health: curl http://localhost:${APP_PORT}/health"
    echo "  Query:  curl -X POST http://localhost:${APP_PORT}/query -H 'Content-Type: application/json' -d '{\"prompt\": \"hello\"}'"
    echo ""
}

# Setup PM2 startup script
setup_startup() {
    print_status "Setting up PM2 startup script..."
    
    print_warning "This will configure PM2 to start automatically on system boot."
    read -p "Continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Startup setup cancelled"
        return 0
    fi
    
    pm2 startup
    print_success "PM2 startup configured. Follow the instructions above if any."
}

# Main menu
show_menu() {
    clear
    print_header "🚀 DeFi Analytics Agent - PM2 Manager"
    echo ""
    print_menu_item "1." "Install PM2"
    print_menu_item "2." "Start Application"
    print_menu_item "3." "Stop Application"
    print_menu_item "4." "Restart Application"
    print_menu_item "5." "Delete Application"
    print_menu_item "6." "Show Status"
    print_menu_item "7." "Show Logs"
    print_menu_item "8." "Monitor Application"
    print_menu_item "9." "Setup Startup Script"
    print_menu_item "0." "Exit"
    echo ""
}

# Main execution
main() {
    # Check prerequisites
    check_nodejs
    check_npm
    
    while true; do
        show_menu
        read -p "Select an option (0-9): " choice
        echo ""
        
        case $choice in
            1)
                install_pm2
                ;;
            2)
                start_app
                ;;
            3)
                stop_app
                ;;
            4)
                restart_app
                ;;
            5)
                delete_app
                ;;
            6)
                show_status
                ;;
            7)
                show_logs
                ;;
            8)
                monitor_app
                ;;
            9)
                setup_startup
                ;;
            0)
                print_success "Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please select 0-9."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
    done
}

# Run main function
main
