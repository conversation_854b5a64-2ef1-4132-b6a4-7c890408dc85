
from typing import List, Dict, Any
from pydantic import BaseModel
from langchain.tools import StructuredTool
from pymongo import MongoClient
import json
import logging
import os
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(filename="app.log", level=logging.DEBUG, format="%(asctime)s - %(levelname)s - %(message)s")

# Load environment variables
load_dotenv()
MONGO_URI = os.getenv("MONGO_URI")
if not MONGO_URI:
    logging.error("MONGO_URI not found in .env")
    raise ValueError("MONGO_URI not found in .env")

# Schema for collections
COLLECTION_SCHEMAS = {
    "swaps": {
        "fields": {
            "account_id": "string",
            "amountIn": "string",
            "amountInUSD": "string -> double",
            "amountOut": "string",
            "amountOutUSD": "string -> double",
            "timestamp": "string (UNIX)",
            "inputTokenAmounts": "array of string",
            "inputTokens": "array of [id, name, symbol, decimals]",
            "pool": "[id, name]",
            "protocol": "[id, name, network]",
            "tickLower": "string|null",
            "tickUpper": "string|null",
            "reserveAmount": "array of string"
        },
        "cast": {
            "amountInUSD": {"$toDouble": "$amountInUSD"},
            "amountOutUSD": {"$toDouble": "$amountOutUSD"},
            "timestamp": {"$toLong": "$timestamp"}
        }
    },
    "deposits": {
        "fields": {
            "account_id": "string",
            "amountIn": "string",
            "amountInUSD": "string -> double",
            "tokenIn": "[id, symbol]",
            "tokenOut": "[id, symbol]",
            "pool": "[id, name]",
            "timestamp": "string (UNIX)"
        },
        "cast": {
            "amountInUSD": {"$toDouble": "$amountInUSD"},
            "timestamp": {"$toLong": "$timestamp"}
        }
    },
    "withdraws": {
        "fields": {
            "account_id": "string",
            "amountUSD": "string -> double",
            "inputTokens": "array of [$raw: string, id]",
            "pool": "[id, name]",
            "timestamp": "string (Unix)",
            "tickLower": "string",
            "tickUpper": "string",
            "protocol": "[id, name, network]"
        },
        "cast": {
            "amountUSD": {"$toDouble": "$amountUSD"},
            "timestamp": {"$toLong": "$timestamp"}
        }
    },
    "Z-Score": {
        "fields": {
            "wallet_id": "string",
            "aggregated_lp_score": "int",
            "lp_scores": "array of pool-level LP info with numeric scores",
            "lp_scores.total_deposit_all_time": "double",
            "lp_scores.total_withdraw_all_time": "double",
            "lp_scores.score_breakdown.*": "int or float",
            "lp_category_breakdown": "[stable-stable, stable-volatile, volatile-volatile]: int"
        },
        "cast": {
            "aggregated_lp_score": {"$toInt": "$aggregated_lp_score"}
        }
    },
    "success": {
        "fields": {
            "wallet_address": "string",
            "protocolScores": "array of [name: string, user_out: array of [supply: int, average_time_between_liquidations: float, liquidation: int, repay: int, borrow: int, average_time_to_first_repay: float, average_time_between_deposits: float, cluster: int, zscore_upper_bound: int, zscore: string, deposit_mix(%volatile): float, average_time_between_borrows: float, borrow_mix(%volatile): float, zscore_lower_bound: int]]"
        },
        "cast": {}  # No casting needed for lp_usecases.py pipeline
    }
}

# Inject $addFields for type casting
def inject_casting_fields(collection: str, pipeline: list) -> list:
    cast_stage = COLLECTION_SCHEMAS.get(collection, {}).get("cast")
    if cast_stage:
        logging.info(f"Injecting cast stage for {collection}: {json.dumps(cast_stage, indent=2)}")
        print(f"📡 Injecting cast stage for {collection}")
        return [{"$addFields": cast_stage}] + pipeline
    return pipeline

# Get MongoDB database
def get_db_for_collection(collection: str):
    try:
        logging.info(f"MONGO_URI: {MONGO_URI}")
        print(f"📡 MONGO_URI: {MONGO_URI}")
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
        client.server_info()  # Check connection
        if collection == "success":
            db = client["test-tx-collector"]
            logging.info(f"Directly using database: test-tx-collector for collection: {collection}")
            print(f"📡 Directly using database: test-tx-collector for collection: {collection}")
        else:
            from db import get_db
            db = get_db(collection if collection in ["summaries", "Z-Score"] else "default")
            logging.info(f"Using db.py for database: {db.name} for collection: {collection}")
            print(f"📡 Using db.py for database: {db.name} for collection: {collection}")
        return db
    except Exception as e:
        logging.error(f"Failed to connect to MongoDB: {str(e)}")
        print(f"❌ Failed to connect to MongoDB: {str(e)}")
        raise ConnectionError(f"Failed to connect to MongoDB: {str(e)}")

# Schema for LangChain input
class MongoQueryInput(BaseModel):
    collection: str
    pipeline: List[Dict[str, Any]]

# Mongo query executor
def run_custom_mongo_query(collection: str, pipeline: List[Dict[str, Any]]) -> str:
    try:
        logging.info(f"Executing query on {collection} with allowDiskUse=True")
        logging.info(f"Pipeline: {json.dumps(pipeline, indent=2)}")
        print(f"📡 Executing query on {collection} with allowDiskUse=True")

        # Debug: Sample documents
        if collection == "success":
            client = MongoClient(MONGO_URI)
            db = client["test-tx-collector"]
            sample = list(db[collection].find().limit(1))
            logging.info(f"Sample document from {collection}: {json.dumps(sample, indent=2, default=str)}")
            print(f"📊 Sample document from {collection}: {json.dumps(sample, indent=2, default=str)}")

        # Inject type casting
        pipeline = inject_casting_fields(collection, pipeline)

        # Add $limit before $group
        new_pipeline = []
        limit_inserted = False
        for stage in pipeline:
            if not limit_inserted and "$group" in stage:
                new_pipeline.append({"$limit": 100000})
                limit_inserted = True
            new_pipeline.append(stage)

        db = get_db_for_collection(collection)
        logging.info(f"Connected to database: {db.name}, collection: {collection}")
        print(f"📡 Connected to database: {db.name}, collection: {collection}")
        results = list(db[collection].aggregate(new_pipeline, allowDiskUse=True))
        logging.info(f"Query on {collection} returned {len(results)} documents")
        print(f"📊 Query on {collection} returned {len(results)} documents")
        return json.dumps(results, indent=2, default=str)

    except Exception as e:
        error_msg = f"Error running MongoDB query: {str(e)}"
        logging.error(error_msg)
        print(error_msg)
        return error_msg

# LangChain StructuredTool
MongoQueryTool = StructuredTool.from_function(
    func=run_custom_mongo_query,
    name="MongoQueryTool",
    description="Run a MongoDB aggregation pipeline on a collection.",
    args_schema=MongoQueryInput
)
