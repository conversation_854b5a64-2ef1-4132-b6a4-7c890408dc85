# db.py

from pymongo import MongoClient
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Read MongoDB URI
MONGO_URI = os.getenv("MONGO_URI")
if not MONGO_URI:
    raise ValueError("MONGO_URI not found in environment. Ensure it's set in .env.")

# Define database names
DB_NAMES = {
    "default": "dex-temp-db",
    "success": "test-tx-collector",
    "summaries": "test-tx-collector"
}

# Return database instance
def get_db(db_name="default"):
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
        client.server_info()  # Check connection
        return client[DB_NAMES.get(db_name, DB_NAMES["default"])]
    except Exception as e:
        raise ConnectionError(f"Failed to connect to MongoDB: {str(e)}")