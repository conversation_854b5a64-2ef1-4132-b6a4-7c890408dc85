
"""
lp_usecases.py
Handles Lending Protocol specific use cases for the ZeruScoreAgent.
"""

import os
import requests
import json
import re
from dotenv import load_dotenv
from llm_utils import generate_llm_prompt
from mongo_tool import run_custom_mongo_query
from llm_agent import query_agent
from db import get_db
import logging

# Configure logging
logging.basicConfig(filename="app.log", level=logging.DEBUG, format="%(asctime)s - %(levelname)s - %(message)s")

# Load secrets
load_dotenv()
MONGO_URI = os.getenv("MONGO_URI")
API_KEY = os.getenv("ZPASS_API_KEY")
if not MONGO_URI or not API_KEY:
    logging.error("MONGO_URI or ZPASS_API_KEY not found in .env")
    print("❌ Error: MONGO_URI or ZPASS_API_KEY not found in .env")
    raise ValueError("MONGO_URI or ZPASS_API_KEY not found in .env")

# Comprehensive field mappings for protocol-specific user_out fields
FIELD_MAPPINGS = {
    "aave": {
        "deposit": "supply",
        "borrow": "borrow",
        "repay": "repay",
        "liquidation": "liquidation",
        "time_prefix": "average_time_",
        "time_metrics": ["between_borrows", "between_deposits", "between_liquidations", "to_first_repay"],
        "mix_metrics": ["borrow_mix(%volatile)", "deposit_mix(%volatile)"],
        "cluster": "cluster",
        "zscore_fields": ["zscore", "zscore_lower_bound", "zscore_upper_bound"]
    },
    "compound_v2": {
        "deposit": "deposit_count",
        "borrow": "borrow_count",
        "repay": "repay_count",
        "liquidation": "liquidation_count",
        "withdraw": "withdraw_count",
        "time_prefix": "median_days_",
        "time_metrics": ["between_borrows", "between_repays", "between_deposits", "between_withdraws"],
        "mix_metrics": ["borrow_mix(%volatile)", "deposit_mix(%volatile)"],
        "cluster": "cluster",
        "zscore_fields": ["zscore", "zscore_lower_bound", "zscore_upper_bound"]
    },
    "compound_v3": {
        "deposit": "deposit_count",
        "borrow": "borrow_count",
        "repay": "repay_count",
        "withdraw": "withdraw_count",
        "liquidation": "liquidation_count", 
        "time_prefix": "median_days_",
        "time_metrics": ["between_borrows", "between_repays", "between_deposits", "between_withdraws"],
        "mix_metrics": ["borrow_mix(%volatile)", "deposit_mix(%volatile)"],
        "cluster": "cluster",
        "zscore_fields": ["zscore", "zscore_lower_bound", "zscore_upper_bound"]
    }
}

def build_dynamic_projection(protocol, fields_to_project):
    projection = {"wallet_address": 1}
    mappings = FIELD_MAPPINGS.get(protocol.lower(), {})
    for field in fields_to_project:
        mapped_field = mappings.get(field, field)
        if mapped_field and field != "liquidation" or (field == "liquidation" and mapped_field):
            projection[field] = {
                "$cond": [
                    {"$gt": [f"$protocolScores.user_out.{mapped_field}", None]},
                    f"$protocolScores.user_out.{mapped_field}",
                    0 if field in ["borrow", "repay", "liquidation", "deposit", "withdraw"] else 0.0
                ]
            }
    return projection

def fetch_wallets_by_protocol(protocol_name, min_score, max_score, limit=10):
    url = f'https://api.myzscore.ai/zpass/api/zscore/wallets/protocol?protocol_name={protocol_name}&min_score={min_score}&max_score={max_score}&limit={limit}'
    headers = {"x-api-key": API_KEY}
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        logging.info(f"API Response for {protocol_name}: {response.status_code}")
        print(f"📡 API Response for {protocol_name}: {response.status_code}")
        return response.json().get("wallets", [])
    except requests.RequestException as e:
        logging.error(f"API request failed for {protocol_name}: {e}")
        print(f"❌ API request failed for {protocol_name}: {e}")
        return []

def fetch_protocol_analytics(protocol_name):
    url = f'https://api.myzscore.ai/zpass/api/analytics/global/{protocol_name}'
    headers = {"x-api-key": API_KEY}
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        logging.info(f"API Analytics Response for {protocol_name}: {response.status_code}")
        print(f"📡 API Analytics Response for {protocol_name}: {response.status_code}")
        return response.json()
    except requests.RequestException as e:
        logging.error(f"API analytics request failed for {protocol_name}: {e}")
        print(f"❌ API analytics request failed for {protocol_name}: {e}")
        return {}

def handle_lp_usecase(wallet, score, memory, use_case, top_n=10, protocol=None, min_score=0, max_score=1000):
    response = {
        "wallet": wallet,
        "score": score,
        "memory": memory,
        "group_prompt": None,
        "wallets": []
    }
    logging.info(f"Handling use case: {use_case}, wallet: {wallet}, protocol: {protocol}")
    print(f"⚙️ Handling use case: {use_case}, wallet: {wallet}, protocol: {protocol}")

    if use_case == "lp_score_filtered_wallets":
        raw_prompt = memory.get("_raw_prompt", "").lower()
        activity_keywords = [
            "borrow", "repay", "liquidation", "deposit", "supply", "withdraw",
            "time", "days", "mix", "volatile", "cluster", "zscore", "z-score"
        ]
        is_activity_based = any(re.search(rf"\b{k}\b", raw_prompt) for k in activity_keywords)

        if is_activity_based:
            # Debug: Protocol names
            pipeline = [
                {"$unwind": {"path": "$protocolScores", "preserveNullAndEmptyArrays": False}},
                {"$group": {"_id": "$protocolScores.name"}},
                {"$sort": {"_id": 1}},
                {"$limit": 100}
            ]
            protocol_names_result = run_custom_mongo_query("success", pipeline)
            protocol_names = [doc["_id"] for doc in json.loads(protocol_names_result)] if protocol_names_result and not protocol_names_result.startswith("Error") else []
            logging.info(f"Debug: Available protocol names: {protocol_names}")
            print(f"📊 Debug: Available protocol names: {protocol_names}")

            # Debug: Total documents
            pipeline = [{"$group": {"_id": None, "count": {"$sum": 1}}}]
            count_result = run_custom_mongo_query("success", pipeline)
            total_count = json.loads(count_result)[0]["count"] if count_result and not count_result.startswith("Error") and json.loads(count_result) else 0
            logging.info(f"Debug: Total documents in success: {total_count}")
            print(f"📊 Debug: Total documents in success: {total_count}")

            # Debug: Schema
            pipeline = [
                {"$match": {"protocolScores": {"$exists": True}}},
                {"$limit": 1},
                {"$project": {"protocolScores": 1, "_id": 0}}
            ]
            schema_result = run_custom_mongo_query("success", pipeline)
            schema_info = json.loads(schema_result) if schema_result and not schema_result.startswith("Error") else []
            logging.info(f"Debug: Schema sample: {json.dumps(schema_info, indent=2)}")
            print(f"📊 Debug: Schema sample: {json.dumps(schema_info, indent=2)}")

            # Determine fields to project based on prompt
            fields_to_project = ["zscore", "zscore_lower_bound", "zscore_upper_bound", "cluster", "borrow_mix(%volatile)", "deposit_mix(%volatile)"]
            mappings = FIELD_MAPPINGS.get(protocol.lower(), {})
            event_fields = ["borrow", "repay", "liquidation", "deposit", "withdraw"]
            for field in event_fields:
                if field in raw_prompt and (mappings.get(field) or field in raw_prompt):
                    fields_to_project.append(field)
            time_prefix = mappings.get("time_prefix", "average_time_")
            time_metrics = mappings.get("time_metrics", [])
            for metric in time_metrics:
                if metric.replace("_", " ") in raw_prompt or "time" in raw_prompt or "days" in raw_prompt:
                    fields_to_project.append(f"{time_prefix}{metric}")

            # Handle combined metrics (e.g., high deposits and low volatile borrows)
            match_conditions = []
            if "high" in raw_prompt and "low" in raw_prompt:
                for field in fields_to_project:
                    if f"high {field}" in raw_prompt:
                        match_conditions.append({field: {"$gte": 10 if field in event_fields else 50.0}})
                    if f"low {field}" in raw_prompt:
                        match_conditions.append({field: {"$lte": 5 if field in event_fields else 10.0}})

            # Build dynamic projection
            projection = build_dynamic_projection(protocol, fields_to_project)

            # Main pipeline
            pipeline = [
                {"$unwind": {"path": "$protocolScores", "preserveNullAndEmptyArrays": False}},
                {"$match": {"protocolScores.name": {"$regex": f"^{protocol}$", "$options": "i"}}},
                {"$unwind": {"path": "$protocolScores.user_out", "preserveNullAndEmptyArrays": True}},
                {"$project": projection},
                {"$match": {
                    "$or": [
                        {f: {"$gt": 0}} for f in fields_to_project if f in event_fields or f in [f"{time_prefix}{m}" for m in time_metrics]
                    ]
                } if not match_conditions else {"$and": match_conditions}},
                {"$match": {
                    "zscore_lower_bound": {"$gte": min_score},
                    "zscore_upper_bound": {"$lte": max_score}
                } if min_score > 0 or max_score < 1000 else {}},
                {"$match": {"cluster": int(re.search(r"cluster (\d+)", raw_prompt).group(1))} if re.search(r"cluster \d+", raw_prompt) else {}},
                {"$group": {
                    "_id": "$wallet_address",
                    **{f: {"$sum": f"${f}"} for f in fields_to_project if f not in ["zscore_lower_bound", "zscore_upper_bound", "cluster"]},
                    "cluster": {"$first": "$cluster"}
                }},
                {"$match": {
                    "$or": [
                        {f: {"$gt": 0}} for f in fields_to_project if f in event_fields or f in [f"{time_prefix}{m}" for m in time_metrics]
                    ]
                } if not match_conditions else {"$and": [{f: {"$gt": 0}} for f in fields_to_project if f in event_fields or f in [f"{time_prefix}{m}" for m in time_metrics]]}},
                {"$sort": {
                    **{f: -1 for f in fields_to_project if f in raw_prompt and f not in ["zscore_lower_bound", "zscore_upper_bound", "cluster"]},
                    "borrow": -1,
                    "repay": -1
                }},
                {"$limit": top_n}
            ]
            logging.info(f"Executing main pipeline: {json.dumps(pipeline, indent=2)}")
            print(f"📜 Executing main pipeline")
            raw_result = run_custom_mongo_query("success", pipeline)
            result = json.loads(raw_result) if isinstance(raw_result, str) and not raw_result.startswith("Error") else []
            logging.info(f"MongoDB Result: {json.dumps(result, indent=2)}")
            print(f"📊 MongoDB Result: {json.dumps(result, indent=2)}")

            if not result:
                response["note"] = f"No data found in MongoDB for {protocol}"
                prompt = f"""
You are a DeFi lending protocol analyst. No active wallets were found in {protocol} based on activity.

User Prompt: {memory['_raw_prompt']}

Debug Info:
- Available protocol names: {protocol_names}
- Total documents: {total_count}
- Schema sample: {json.dumps(schema_info, indent=2)}
-

Response:
No active wallets found in {protocol}. Likely causes:
1. Protocol name mismatch (expected '{protocol}', found: {protocol_names}).
2. Empty or missing data in `success` collection.
3. Schema mismatch.

Next Steps:
1. Check protocol names: `db.success.distinct('protocolScores.name')`
2. Inspect schema: `db.success.findOne()`
3. Verify data: `db.success.countDocuments()`
4. Run: `python check_success.py` to compare
"""
                response["group_prompt"] = prompt
                return response

            wallets_analyzed = [doc["_id"] for doc in result]
            metrics_summary = {f: sum(doc.get(f, 0) for doc in result) for f in fields_to_project if f not in ["zscore_lower_bound", "zscore_upper_bound", "cluster"]}
            avg_metrics = {f: v / len(result) for f, v in metrics_summary.items()}
            prompt = f"""
You are a DeFi lending protocol analyst. The following data from the MongoDB `success` collection shows the top {len(result)} wallets in {protocol} based on activity.

User Prompt: {memory['_raw_prompt']}

MongoDB Output:
{json.dumps(result, indent=2)}

Wallets Analyzed: {', '.join(wallets_analyzed)}

Summarize the overall lending behavior, risks, and trends for these wallets. Include: whatever u get in Mongodb output and give analysis please dont mention data insufficient or anything like that just use the data you are given.

Use DeFi terminology and highlight risk factors (e.g., high liquidation counts, high volatile asset usage, or long time intervals indicating inactivity).
"""
            response["group_prompt"] = prompt
            response["wallets"] = wallets_analyzed
            return response

        # Use Z Pass API for non-activity-based score filtering
        wallets = fetch_wallets_by_protocol(protocol, min_score, max_score, limit=top_n)
        response["wallets"] = wallets

        print(f"📡 Requested wallets for: {protocol}, score {min_score}-{max_score}")
        print(f"📥 Wallets received: {len(wallets)}")

        if not wallets:
            response["note"] = "No wallets found in API"
            try:
                fallback_query = query_agent.invoke(memory["_raw_prompt"])
                logging.info(f"Fallback Query: {json.dumps(fallback_query, indent=2)}")
                print(f"📜 Fallback Query: {json.dumps(fallback_query, indent=2)}")
                if isinstance(fallback_query, dict) and fallback_query.get("collection") == "success":
                    pipeline = fallback_query.get("pipeline", [])
                    raw_result = run_custom_mongo_query("success", pipeline)
                    result = json.loads(raw_result) if isinstance(raw_result, str) and not raw_result.startswith("Error") else []
                    logging.info(f"MongoDB Result: {json.dumps(result, indent=2)}")
                    print(f"📊 MongoDB Result: {json.dumps(result, indent=2)}")

                    if not result:
                        response["note"] = "No data found in MongoDB for the query"
                        prompt = f"""
You are a DeFi lending protocol analyst. No data was found for wallets in {protocol} based on z-score filtering.

User Prompt: {memory['_raw_prompt']}

Response:
No wallets found in {protocol} within the score range {min_score}-{max_score}. This may indicate low engagement or missing data.
"""
                        response["group_prompt"] = prompt
                        return response

                    wallets_analyzed = [doc["_id"] for doc in result]
                    fields_to_project = [f for f in result[0].keys() if f not in ["_id", "zscore_lower_bound", "zscore_upper_bound", "cluster"]]
                    metrics_summary = {f: sum(doc.get(f, 0) for doc in result) for f in fields_to_project}
                    avg_metrics = {f: v / len(result) for f, v in metrics_summary.items()}
                    prompt = f"""
You are a DeFi lending protocol analyst. The following data from the MongoDB `success` collection shows the top {len(result)} wallets in {protocol} based on z-score filtering.

User Prompt: {memory['_raw_prompt']}

MongoDB Output:
{json.dumps(result, indent=2)}

Wallets Analyzed: {', '.join(wallets_analyzed)}

Summarize the overall lending behavior, risks, and trends for these wallets. Include: whatever u get in Mongodb output and give analysis please dont mention data insufficient or anything like that just use the data you are given.
Use DeFi terminology and highlight risk factors.
"""
                    response["group_prompt"] = prompt
                    response["wallets"] = wallets_analyzed
                else:
                    response["note"] = "Invalid fallback query generated"
                    prompt = f"""
You are a DeFi lending protocol analyst. An invalid MongoDB query was generated for {protocol}.

User Prompt: {memory['_raw_prompt']}

Response:
Unable to retrieve wallet activity data for {protocol} due to an invalid query. Please try again or contact support.
"""
                    response["group_prompt"] = prompt
            except Exception as e:
                response["note"] = f"LP fallback to Mongo failed: {str(e)}"
                logging.error(f"LP fallback to Mongo failed: {e}")
                print(f"❌ LP fallback to Mongo failed: {e}")
            return response

        # Fetch summaries from Wallet_summaries collection
        db = get_db("summaries")
        summaries_col = db["Wallet_summaries"]
        logging.info(f"Connected to database: {db.name}, collection: Wallet_summaries")
        print(f"📡 Connected to database: {db.name}, collection: Wallet_summaries")

        summaries = []
        for w in wallets:
            address = w if isinstance(w, str) else w.get("walletAddress", "")
            if not address:
                continue
            doc = summaries_col.find_one({"walletAddress": {"$regex": f"^{address}$", "$options": "i"}})
            if not doc:
                doc = summaries_col.find_one({"walletAddress": {"$regex": address[-6:], "$options": "i"}})
            if doc and "summary" in doc:
                summary = doc["summary"]
                logging.info(f"Wallet {address} summary: {json.dumps(summary, default=str)}")
                if isinstance(summary, dict):
                    summary_str = ", ".join(f"{k}: {v}" for k, v in summary.items() if isinstance(v, (str, int, float)))
                    summaries.append(summary_str)
                elif isinstance(summary, str):
                    summaries.append(summary)
                else:
                    logging.warning(f"Unexpected summary type for {address}: {type(summary)}")
                    summaries.append(str(summary))

        logging.info(f"Found {len(summaries)} summaries out of {len(wallets)} wallets")
        print(f"🧠 Found {len(summaries)} summaries out of {len(wallets)} wallets")

        if summaries:
            try:
                combined_summary = "\n".join(f"- {s}" for s in summaries)
                prompt = f"""
You are a DeFi lending protocol analyst. The following are behavior summaries of {len(summaries)} wallets active in {protocol} between scores {min_score} and {max_score}:

{combined_summary}

Wallets Analyzed: {', '.join(str(w) if isinstance(w, str) else w.get('walletAddress', 'Unknown') for w in wallets)}

Summarize their overall lending behavior, risk, and potential actions. Highlight significant activity metrics (e.g., high deposits, liquidations, volatile asset usage, or time intervals).
"""
                response["group_prompt"] = prompt
            except Exception as e:
                response["note"] = f"Failed to combine summaries: {str(e)}"
                logging.error(f"Failed to combine summaries: {e}")
                print(f"❌ Failed to combine summaries: {e}")
                prompt = f"""
You are a DeFi lending protocol analyst. Summaries for {len(wallets)} wallets in {protocol} could not be combined due to an error.

User Prompt: {memory['_raw_prompt']}

Response:
Retrieved {len(wallets)} wallets, but failed to generate a combined summary. Contact support for assistance.
"""
                response["group_prompt"] = prompt
        else:
            response["note"] = "No summaries found in Wallet_summaries collection"
            prompt = f"""
You are a DeFi lending protocol analyst. No behavior summaries were found for wallets in {protocol}.

User Prompt: {memory['_raw_prompt']}

Response:
No detailed behavior summaries were available for the top {top_n} wallets in {protocol}. However, {len(wallets)} wallets were identified as active.
"""
            response["group_prompt"] = prompt

        return response

    elif use_case == "lp_wallet_behavior_analysis":
        if not wallet or wallet == "":
            response["note"] = "No valid wallet address provided for behavior analysis"
            prompt = f"""
You are a DeFi lending protocol analyst. No valid wallet address was provided for analysis in {protocol}.

User Prompt: {memory['_raw_prompt']}

Response:
Please provide a valid wallet address to analyze its behavior in {protocol}.
"""
            response["group_prompt"] = prompt
            return response

        protocol = protocol or "All Protocols"
        url = f"https://api.myzscore.ai/zpass/api/analytics/wallet/{wallet}/{protocol}?timeTravel=90"
        headers = {"x-api-key": API_KEY}

        print(f"📡 Fetching wallet behavior for {wallet} on {protocol}")
        try:
            r = requests.get(url, headers=headers)
            r.raise_for_status()
            data = r.json()
            summary = data.get("walletSummary", "")
            category = data.get("categorySummary", "")
            rank = data.get("rank")
            percentile = data.get("percentile")
            chain_usage = data.get("chainUsage", [])

            prompt = f"""
You are a DeFi lending protocol analyst. Below is the behavior summary for wallet {wallet} on {protocol}:

📊 Wallet Summary: {summary}

📈 Risk Cluster: {category}
📌 Rank: {rank} out of {data.get('totalWallets')}, Percentile: {percentile}
"""
            if chain_usage:
                prompt += "\n🧪 Chain Usage:\n"
                for chain in chain_usage:
                    prompt += f"- Chain: {chain.get('chainName')}, Transactions: {chain.get('transactionCount')}, Total Liquidations: {chain.get('totalLiquidations')}\n"

            prompt += "\nGenerate a detailed DeFi behavioral profile for this wallet, including all activity metrics (deposits, borrows, repays, withdrawals, liquidations, volatile assets, time intervals)."
            response["group_prompt"] = prompt
        except requests.RequestException as e:
            response["note"] = f"API failed with status: {str(e)}"
            logging.error(f"API request failed for wallet {wallet}: {e}")
            print(f"❌ API request failed for wallet {wallet}")
            try:
                mappings = FIELD_MAPPINGS.get(protocol.lower(), {})
                fields_to_project = [
                    *mappings.get("zscore_fields", []),
                    mappings.get("cluster", "cluster"),
                    *mappings.get("mix_metrics", []),
                    *(mappings.get(f, f) for f in ["borrow", "repay", "liquidation", "deposit", "withdraw"] if mappings.get(f)),
                    *(f"{mappings.get('time_prefix', 'average_time_')}{m}" for m in mappings.get("time_metrics", []))
                ]
                projection = {k: {"$ifNull": [f"$protocolScores.user_out.{k}", 0 if k in mappings.values() and "count" in k else 0.0]} for k in fields_to_project}

                fallback_query = [
                    {"$unwind": {"path": "$protocolScores", "preserveNullAndEmptyArrays": False}},
                    {"$match": {
                        "wallet_address": wallet,
                        "protocolScores.name": {"$regex": f"^{protocol}$", "$options": "i"}
                    }},
                    {"$unwind": {"path": "$protocolScores.user_out", "preserveNullAndEmptyArrays": True}},
                    {"$project": projection}
                ]
                raw_result = run_custom_mongo_query("success", fallback_query)
                result = json.loads(raw_result) if isinstance(raw_result, str) and not raw_result.startswith("Error") else []
                logging.info(f"MongoDB Result: {json.dumps(result, indent=2)}")
                print(f"📊 MongoDB Result: {json.dumps(result, indent=2)}")

                prompt = f"""
You are a DeFi lending protocol analyst. This is the output from the MongoDB `success` collection for wallet {wallet} in {protocol}.

User Prompt: {memory['_raw_prompt']}

MongoDB Output:
{json.dumps(result, indent=2)}

Summarize protocol-level wallet activity (all metrics including deposits, borrows, repays, withdrawals, liquidations, time intervals, volatile assets, and cluster) and highlight risks or trends. Use DeFi terminology.
"""
                response["group_prompt"] = prompt
            except Exception as e:
                response["note"] = f"LP fallback to Mongo failed: {str(e)}"
                logging.error(f"LP fallback to Mongo failed: {e}")
                print(f"❌ LP fallback to Mongo failed: {e}")
            return response

    elif use_case == "lp_protocol_comparison":
        proto1 = memory.get("protocol_1", "aave")
        proto2 = memory.get("protocol_2", "compound_v2")
        raw_prompt = memory.get("_raw_prompt", "").lower()
        fields_to_project = ["borrow", "repay", "liquidation", "deposit", "withdraw", "borrow_mix(%volatile)", "deposit_mix(%volatile)"]
        mappings1 = FIELD_MAPPINGS.get(proto1.lower(), {})
        mappings2 = FIELD_MAPPINGS.get(proto2.lower(), {})
        time_metrics1 = mappings1.get("time_metrics", [])
        time_metrics2 = mappings2.get("time_metrics", [])
        time_prefix1 = mappings1.get("time_prefix", "average_time_")
        time_prefix2 = mappings2.get("time_prefix", "median_days_")
        for metric in set(time_metrics1 + time_metrics2):
            if metric.replace("_", " ") in raw_prompt or "time" in raw_prompt or "days" in raw_prompt:
                fields_to_project.append(f"{time_prefix1}{metric}" if metric in time_metrics1 else f"{time_prefix2}{metric}")

        # Fetch global analytics
        data1 = fetch_protocol_analytics(proto1)
        data2 = fetch_protocol_analytics(proto2)

        if not data1 or not data2:
            # MongoDB fallback with dynamic fields
            pipeline = [
                {"$unwind": {"path": "$protocolScores", "preserveNullAndEmptyArrays": False}},
                {"$match": {"protocolScores.name": {"$in": [proto1, proto2]}}},
                {"$unwind": {"path": "$protocolScores.user_out", "preserveNullAndEmptyArrays": True}},
                {"$project": {
                    "protocol": "$protocolScores.name",
                    "wallet_address": 1,
                    **{f: {
                        "$cond": [
                            {"$gt": [f"$protocolScores.user_out.{FIELD_MAPPINGS.get(p.lower(), {}).get(f, f)}", None]},
                            f"$protocolScores.user_out.{FIELD_MAPPINGS.get(p.lower(), {}).get(f, f)}",
                            0 if f in ["borrow", "repay", "liquidation", "deposit", "withdraw"] else 0.0
                        ]
                    } for p in [proto1, proto2] for f in fields_to_project if FIELD_MAPPINGS.get(p.lower(), {}).get(f, f)}
                }},
                {"$group": {
                    "_id": "$protocol",
                    "wallet_count": {"$addToSet": "$wallet_address"},
                    **{f: {"$sum": f"${f}"} for f in fields_to_project}
                }},
                {"$project": {
                    "protocol": "$_id",
                    "active_wallets": {"$size": "$wallet_count"},
                    **{f: 1 for f in fields_to_project}
                }}
            ]
            raw_result = run_custom_mongo_query("success", pipeline)
            result = json.loads(raw_result) if isinstance(raw_result, str) and not raw_result.startswith("Error") else []
            logging.info(f"MongoDB Fallback Result: {json.dumps(result, indent=2)}")
            print(f"📊 MongoDB Fallback Result: {json.dumps(result, indent=2)}")

            data1 = next((d for d in result if d["protocol"].lower() == proto1.lower()), {"protocol": proto1, "active_wallets": 0, **{f: 0 for f in fields_to_project}})
            data2 = next((d for d in result if d["protocol"].lower() == proto2.lower()), {"protocol": proto2, "active_wallets": 0, **{f: 0 for f in fields_to_project}})

        prompt = f"""
You are a DeFi lending protocol analyst. Compare the following two DeFi protocols based on global metrics:

🔷 {proto1.upper()}:
- Active Wallets: {data1.get('active_wallets', 'Unknown')}
{''.join(f"- Total {f}: {data1.get(f, 'Unknown')}" for f in fields_to_project)}

🔶 {proto2.upper()}:
- Active Wallets: {data2.get('active_wallets', 'Unknown')}
{''.join(f"- Total {f}: {data2.get(f, 'Unknown')}" for f in fields_to_project)}

User Prompt: {memory['_raw_prompt']}

Summarize differences and similarities in user base and activity metrics (deposits, borrows, repays, withdrawals, liquidations, volatile assets, time intervals). Highlight risk factors (e.g., high volatile asset usage, liquidations). Use DeFi terminology. Note data gaps if present.
"""
        response["group_prompt"] = prompt
        return response

    return response
