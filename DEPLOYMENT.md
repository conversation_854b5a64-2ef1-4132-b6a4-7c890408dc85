# DeFi Analytics Agent - Production Deployment Guide

This guide provides complete instructions for deploying the DeFi Analytics Agent with HTTPS support and proper CORS configuration.

## 🚀 Quick Start

### 1. Initial Setup
```bash
# Run the initial setup (if not done already)
./setup.sh

# Test the application locally
./run.sh
```

### 2. Production Deployment with HTTPS
```bash
# Complete deployment with HTTPS and systemd service
./deploy.sh
```

## 📋 Prerequisites

- Ubuntu 20.04+ server
- Domain name pointing to your server IP (agentapi.myzscore.ai)
- Sudo privileges
- Ports 80, 443, and 8001 open

## 🔧 Manual Setup Steps

### Step 1: Basic Application Setup
```bash
# Install dependencies and set up virtual environment
./setup.sh

# Update .env file with your actual API keys
nano .env
```

### Step 2: HTTPS and Domain Setup
```bash
# Set up Nginx reverse proxy with SSL certificates
./setup-https.sh

# Or run the complete deployment
./deploy.sh
```

### Step 3: Service Management
```bash
# The application runs as a systemd service
sudo systemctl status defi-agent    # Check status
sudo systemctl restart defi-agent   # Restart service
sudo systemctl stop defi-agent      # Stop service
sudo systemctl start defi-agent     # Start service

# View logs
sudo journalctl -u defi-agent -f    # Follow service logs
```

## 🌐 CORS Configuration

The application is configured to allow requests from:
- `http://localhost:3000`
- `https://localhost:3000`
- `https://agent.myzscore.ai`
- `https://agentapi.myzscore.ai`

### Testing CORS
```bash
# Test CORS configuration
./test-cors.sh
```

## 📡 API Endpoints

### Production (HTTPS)
- Health check: `https://agentapi.myzscore.ai/health`
- Query endpoint: `https://agentapi.myzscore.ai/query`

### Local Development
- Health check: `http://localhost:8001/health`
- Query endpoint: `http://localhost:8001/query`

### External IP Access
- Health check: `http://YOUR_SERVER_IP:8001/health`
- Query endpoint: `http://YOUR_SERVER_IP:8001/query`

## 🔒 Security Features

- **SSL/TLS**: Automatic HTTPS with Let's Encrypt certificates
- **CORS**: Restricted to specific origins
- **Rate Limiting**: 10 requests per second per IP
- **Security Headers**: XSS protection, content type sniffing protection
- **Firewall**: UFW configured for necessary ports only

## 📊 Monitoring and Logs

### Application Logs
```bash
# Service logs
sudo journalctl -u defi-agent -f

# Nginx access logs
sudo tail -f /var/log/nginx/agentapi.myzscore.ai.access.log

# Nginx error logs
sudo tail -f /var/log/nginx/agentapi.myzscore.ai.error.log
```

### Health Monitoring
```bash
# Check if service is running
curl https://agentapi.myzscore.ai/health

# Check from localhost:3000 (CORS test)
curl -H "Origin: http://localhost:3000" https://agentapi.myzscore.ai/health
```

## 🛠️ Troubleshooting

### Common Issues

1. **CORS Errors**
   - Verify origin is in the allowed list in `main.py`
   - Check Nginx CORS headers in `/etc/nginx/sites-available/agentapi.myzscore.ai`

2. **SSL Certificate Issues**
   - Ensure domain DNS points to server IP
   - Run: `sudo certbot renew --dry-run`

3. **Service Not Starting**
   - Check logs: `sudo journalctl -u defi-agent -f`
   - Verify .env file has correct values
   - Ensure virtual environment is set up

4. **502 Bad Gateway**
   - Check if FastAPI service is running: `sudo systemctl status defi-agent`
   - Verify port 8001 is not blocked

### Manual Service Restart
```bash
# If automatic deployment fails, manually restart services
sudo systemctl restart defi-agent
sudo systemctl reload nginx
```

## 🔄 Updates and Maintenance

### Updating the Application
```bash
# Pull latest changes
git pull

# Restart the service
sudo systemctl restart defi-agent
```

### SSL Certificate Renewal
Certificates auto-renew via systemd timer. Manual renewal:
```bash
sudo certbot renew
sudo systemctl reload nginx
```

## 📝 Configuration Files

- `main.py` - FastAPI application with CORS configuration
- `nginx-config.conf` - Nginx reverse proxy configuration
- `defi-agent.service` - Systemd service configuration
- `run-production.sh` - Production startup script
- `.env` - Environment variables (keep secure!)

## 🧪 Testing

### Local Testing
```bash
# Test health endpoint
curl http://localhost:8001/health

# Test query endpoint
curl -X POST http://localhost:8001/query \
  -H "Content-Type: application/json" \
  -d '{"prompt": "hello"}'
```

### Production Testing
```bash
# Test HTTPS health endpoint
curl https://agentapi.myzscore.ai/health

# Test CORS from localhost:3000
curl -H "Origin: http://localhost:3000" \
  -X POST https://agentapi.myzscore.ai/query \
  -H "Content-Type: application/json" \
  -d '{"prompt": "hello"}'
```

## ⚠️ Important Notes

1. **DNS Configuration**: Ensure `agentapi.myzscore.ai` points to your server's IP
2. **Firewall**: Only necessary ports (22, 80, 443) are open
3. **API Keys**: Keep your `.env` file secure and never commit it to version control
4. **Monitoring**: Regularly check logs and service status
5. **Backups**: Consider backing up your configuration and SSL certificates

## 🆘 Support

If you encounter issues:
1. Check the logs using the commands above
2. Run `./test-cors.sh` to verify CORS configuration
3. Ensure all prerequisites are met
4. Verify domain DNS configuration
