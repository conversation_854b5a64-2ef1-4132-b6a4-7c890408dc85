"""
Configuration settings for DeFi Analytics Agent
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Configuration
API_HOST = os.getenv("API_HOST", "0.0.0.0")
API_PORT = int(os.getenv("API_PORT", "8001"))

# Database Configuration
MONGO_URI = os.getenv("MONGO_URI")
if not MONGO_URI:
    raise ValueError("MONGO_URI not found in environment. Ensure it's set in .env.")

# Database names mapping
DB_NAMES = {
    "default": "dex-temp-db",
    "success": "test-tx-collector", 
    "summaries": "test-tx-collector"
}

# LLM Configuration
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
FIREWORKS_API_KEY = os.getenv("FIREWORKS_API_KEY")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

# External API Configuration
ZPASS_API_KEY = os.getenv("ZPASS_API_KEY")

# CORS Configuration
CORS_ORIGINS = [
    "http://localhost:3000",
    "https://localhost:3000", 
    "http://agent.myzscore.ai",
    "https://agent.myzscore.ai",
    "https://agentapi.myzscore.ai",
    "http://agentapi.myzscore.ai",
    "https://sentient-ui-alpha.vercel.app",
]

# Error Messages
ERROR_SUGGESTION_MESSAGE = """
Sorry, I can't answer this. Instead, try asking something like:
- ****************************************** how much gas fees has this wallet paid on dex?
-  Rank top OG wallets by cumulative gas fees paid on this DEX.
- analyse top 10 wallets on aave protocol with scores between 800 ad 900
- top 10 traders who trade with usdc and weth 
- What tokens are most commonly swapped
"""

# Logging Configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FILE = "app.log"
LLM_LOG_FILE = "llm_output.log"
