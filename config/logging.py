"""
Logging configuration for DeFi Analytics Agent
"""
import logging
import os
from config.settings import LOG_LEVEL, LOG_FILE, LLM_LOG_FILE

def setup_logging():
    """Configure logging for the application"""
    # Main application logger
    logging.basicConfig(
        filename=LOG_FILE,
        level=getattr(logging, LOG_LEVEL.upper()),
        format="%(asctime)s - %(levelname)s - %(message)s"
    )
    
    # LLM specific logger
    llm_log_handler = logging.FileHandler(LLM_LOG_FILE)
    llm_log_handler.setLevel(logging.DEBUG)
    llm_logger = logging.getLogger("llm_output")
    llm_logger.addHandler(llm_log_handler)
    
    return logging.getLogger(__name__)

def get_logger(name: str):
    """Get a logger instance"""
    return logging.getLogger(name)
