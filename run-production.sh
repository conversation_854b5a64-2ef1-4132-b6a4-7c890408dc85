#!/bin/bash

# DeFi Analytics Agent - Production Run Script
# Use this script to start the application in production mode

set -e

echo "🚀 Starting DeFi Analytics Agent (Production Mode)..."
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_error "Virtual environment not found! Please run setup.sh first."
    exit 1
fi

# Check if .env file exists and has content
if [ ! -f ".env" ]; then
    print_error ".env file not found! Please create it with your API keys."
    exit 1
fi

# Check if .env has placeholder values
if grep -q "your_.*_api_key_here\|your_.*_uri_here" .env; then
    print_error "Please update .env file with your actual API keys and configuration!"
    echo "Edit .env file and replace placeholder values with real ones."
    exit 1
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Check environment variables
print_status "Checking environment variables..."
python3 -c "
import os
from dotenv import load_dotenv
load_dotenv()

required_vars = ['GEMINI_API_KEY', 'GOOGLE_API_KEY', 'MONGO_URI', 'ZPASS_API_KEY', 'FIREWORKS_API_KEY']
missing_vars = []

for var in required_vars:
    if not os.getenv(var):
        missing_vars.append(var)
    else:
        print(f'✅ {var}: Set')

if missing_vars:
    print(f'❌ Missing environment variables: {missing_vars}')
    exit(1)
else:
    print('All environment variables are set!')
"

if [ $? -ne 0 ]; then
    print_error "Environment variables check failed!"
    exit 1
fi

print_success "Environment variables verified"

# Test database connectivity (optional)
print_status "Testing database connectivity..."
python3 -c "
import os
from dotenv import load_dotenv
from pymongo import MongoClient
import sys

load_dotenv()
MONGO_URI = os.getenv('MONGO_URI')

try:
    client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
    client.server_info()
    print('✅ MongoDB connection successful')
except Exception as e:
    print(f'⚠️  MongoDB connection failed: {e}')
    print('The application will start but database queries may fail.')
" 2>/dev/null || print_warning "Database connectivity test failed (application will still start)"

# Start the application in production mode
print_status "Starting FastAPI server in production mode..."
print_success "DeFi Analytics Agent is starting on http://0.0.0.0:8001"
echo ""
echo "📡 Available endpoints:"
echo "  - Health check: GET  http://your-server-ip:8001/health"
echo "  - Query:        POST http://your-server-ip:8001/query"
echo ""
echo "🌐 CORS enabled for:"
echo "  - http://localhost:3000"
echo "  - https://localhost:3000"
echo "  - https://agent.myzscore.ai"
echo "  - https://agentapi.myzscore.ai"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start uvicorn server with production settings
# --workers 4: Use 4 worker processes for better performance
# --host 0.0.0.0: Listen on all interfaces
# --port 8001: Use port 8001
uvicorn main:app --host 0.0.0.0 --port 8001 --workers 4
