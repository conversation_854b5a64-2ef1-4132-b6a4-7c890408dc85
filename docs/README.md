DeFi Analytics Agent README
Overview
This DeFi Analytics Agent is a Python-based application designed to process user queries related to decentralized finance (DeFi) activities, specifically for decentralized exchanges (DEX) and lending protocols (LP) like Aave and Compound. It uses a large language model (LLM) to classify queries, parse them, and generate MongoDB aggregation pipelines or insights, providing actionable analytics for DEX and LP users. The application is built with FastAPI for API integration, MongoDB for data storage, and a modular architecture for extensibility.
The primary entry point is agent.py, which handles user queries via a REST API endpoint (/query). Queries are classified as DEX (e.g., swaps, volumes), LP (e.g., lending, borrowing, zScores), or off-topic (e.g., greetings). Valid queries are processed to generate insights, while errors and off-topic queries return a user-friendly suggestion message.
File Descriptions
Below is a brief description of each key file in the application:

agent.py: The core entry point that orchestrates query processing. It classifies user queries (DEX, LP, or off-topic), routes them to appropriate handlers, and ensures errors return a user-friendly suggestion message.
mongo_tool.py: Handles MongoDB interactions, executing aggregation pipelines for DEX queries and returning results or error messages.
lp_usecases.py: Processes LP queries by generating MongoDB queries or LLM prompts based on parsed query parameters (e.g., wallet, protocol, score range).
query_parser.py: Parses LP queries to extract parameters like wallet addresses, protocols, score ranges, and use cases, returning a structured object for lp_usecases.py.
llm_utils.py: Generates LLM prompts for LP query analysis, ensuring consistent prompt formatting for protocols like Aave or Compound.
llm_agent.py: Interfaces with the LLM (e.g., gemini-1.5-flash) to classify queries, generate MongoDB pipelines for DEX queries, or analyze results.
main.py: Defines the FastAPI application, exposing the /query endpoint to accept user input and invoke agent.py.

Query Processing Flow
Below is a detailed step-by-step explanation of how a user query is processed, covering the role of each file:

User Submits Query:

The user sends a POST request to http://localhost:8001/query with a JSON body containing the prompt field (e.g., {"prompt": "Top users on Aave with scores between 200 and 300"}).
Example command:$response = Invoke-RestMethod -Uri http://localhost:8001/query -Method POST -ContentType "application/json" -Body '{"prompt": "Top users on Aave with scores between 200 and 300"}'
$response | Format-List


File Involved: main.py receives the request via the FastAPI /query endpoint and calls query_agent_function in agent.py.


Query Classification (agent.py):

The classify_prompt_llm function in agent.py uses the LLM (via llm_agent.py) to classify the query into one of three categories:
dex: Queries about swaps, deposits, withdrawals, DEX volumes, or liquidity pools (e.g., "List wallets whose single largest swap exceeded $100,000").
lp: Queries about lending protocols, borrowing, zScores, or wallet behavior (e.g., "Top users on Aave with scores between 200 and 300").
off_topic: Greetings or unrelated queries (e.g., "I love you").


The classification prompt is sent to the LLM, which returns a single word (dex, lp, or off_topic). If classification fails, it defaults to off_topic.
File Involved: agent.py (classification logic), llm_agent.py (LLM invocation).
Logging: The classification result is logged to app.log (e.g., Classified prompt 'Top users on Aave...' as: lp).


Off-Topic Query Handling (agent.py):

If the query is classified as off_topic, agent.py invokes the LLM with a prompt to generate a friendly response (e.g., "Aw, that's sweet!" for "I love you") followed by the ERROR_SUGGESTION_MESSAGE:Sorry, I can't answer this. Instead, try asking something like:
- Compare Aave and Compound V2 activity
- What’s the lending behavior of ****************************************** on Aave?
- Analyze the risk profile of wallet 0x000abc123def... on Compound V3?
- List wallets whose single largest swap exceeded $100k
- What tokens are most commonly swapped


The response is cleaned of markdown artifacts (e.g., :*-, **) using regex.
File Involved: agent.py (off-topic logic), llm_agent.py (LLM invocation).
Output: The cleaned response is returned to the user.
Logging: The response is logged to app.log (e.g., Off-topic response generated for prompt: I love you).


DEX Query Processing (agent.py, mongo_tool.py, llm_agent.py):

If classified as dex, agent.py invokes query_agent.invoke (from llm_agent.py) to generate a MongoDB aggregation pipeline and collection name (e.g., swaps).
The pipeline is parsed from the LLM output, handling formats like dictionaries, JSON strings, or output fields, with error checks for:
Invalid JSON (json.JSONDecodeError).
Missing JSON block (re.search failure).
Missing collection or pipeline fields.


File Involved: agent.py (parsing), llm_agent.py (pipeline generation).
MongoDB Execution: The pipeline is executed via run_custom_mongo_query in mongo_tool.py, querying the specified collection (e.g., swaps).
If the query fails (e.g., invalid collection), it returns an error string starting with "Error".
If the result is empty ([]), an error is triggered.


Analysis: If results are returned, agent.py formats them as JSON and invokes the LLM with an analysis prompt to summarize trends and insights (e.g., "Wallets Analyzed: 0x123...abc").
Error Handling: Any error (parsing, MongoDB failure, empty results) returns only the ERROR_SUGGESTION_MESSAGE, with details logged to app.log.
File Involved: mongo_tool.py (query execution), agent.py (analysis), llm_agent.py (LLM analysis).
Output Example (for "List wallets whose single largest swap exceeded $100,000"):The following wallets executed swaps exceeding $100,000 in value, indicating significant trading activity:
Wallets Analyzed:
- 0x123...abc: Max swap $150,000
- 0x456...def: Max swap $120,000
These wallets demonstrate high-volume trading, with a preference for large transactions, potentially indicating whale activity or arbitrage strategies.


Logging: Pipeline execution and errors are logged to app.log (e.g., Executing MongoDB pipeline: ...).


LP Query Processing (agent.py, query_parser.py, lp_usecases.py, llm_utils.py, llm_agent.py):

If classified as lp, agent.py invokes parse_prompt_llm in query_parser.py to extract parameters:
wallet_address: Specific wallet (e.g., 0x5f723856...) or None for top-N queries.
protocols: List of protocols (e.g., ["aave"]).
score_range: Min and max zScores (e.g., [200, 300]).
use_case: Query type (e.g., lp_score_filtered_wallets).
top_n: Number of results (e.g., 10).


File Involved: query_parser.py (parsing), agent.py (invoking parser).
LP Handling: handle_lp_usecase in lp_usecases.py processes the parsed parameters, generating either a MongoDB query or an LLM prompt (via group_prompt).
Parameters are passed to handle_lp_usecase, including a memory dictionary with defaults (e.g., protocol: "aave", protocol_2: "compound_v2").


Prompt Generation: If no group_prompt is returned, generate_llm_prompt in llm_utils.py creates a prompt for the LLM to analyze the query (e.g., summarizing lending behavior).
LLM Analysis: The prompt is sent to the LLM via llm.invoke in llm_agent.py to generate insights.
Error Handling: Any error (parsing, invalid protocol, empty results, or LLM failures like "high") returns only the ERROR_SUGGESTION_MESSAGE, with details logged to app.log.
File Involved: lp_usecases.py (query handling), llm_utils.py (prompt generation), llm_agent.py (LLM invocation), agent.py (orchestration).
Output Example (for "Top users on Aave with scores between 200 and 300"):The top users on Aave with zScores between 200 and 300 exhibit moderate activity, with consistent lending and borrowing patterns. 
Wallets Analyzed:
- 0x789...xyz
- 0xabc...123
These users maintain stable collateral ratios and regular repayments, indicating reliable creditworthiness.


Logging: Parsed parameters, generated prompt, and errors are logged to app.log (e.g., LP prompt generated: ...).


Error Handling (agent.py):

Errors in any branch (DEX, LP, or classification) return only the ERROR_SUGGESTION_MESSAGE to users:Sorry, I can't answer this. Instead, try asking something like:
- Compare Aave and Compound V2 activity
- What’s the lending behavior of ****************************************** on Aave?
- Analyze the risk profile of wallet 0x000abc123def... on Compound V3?
- List wallets whose single largest swap exceeded $100k
- What tokens are most commonly swapped


Internal details (e.g., "Empty result returned from Mongo", "high") are logged to app.log for debugging but not shown to users.
File Involved: agent.py (error handling), all other files (potential error sources).


Response to User (main.py):

The response (successful result or ERROR_SUGGESTION_MESSAGE) is returned via the FastAPI /query endpoint in main.py as a JSON object: {"response": "<result>"}.
If the response is truncated (e.g., … in PowerShell), it’s a client-side issue.



How to Run
Prerequisites

Python: 3.8 or higher.
MongoDB: A running instance with collections like swaps and aave_users containing DeFi data (e.g., amountInUSD, zScore).
Dependencies: Install required packages:pip install fastapi uvicorn pymongo python-dotenv


LLM Access: Configure access to the LLM (e.g., gemini-1.5-flash) via environment variables in a .env file (see below).
Environment Variables: Create a .env file in the project root with:MONGO_URI=mongodb://localhost:27017
DATABASE_NAME=defi_analytics
LLM_API_KEY=<your-llm-api-key>



Setup

Clone the Repository:git clone <repository-url>
cd <repository-directory>


Install Dependencies:pip install -r requirements.txt

Ensure requirements.txt includes fastapi, uvicorn, pymongo, python-dotenv, and any LLM-specific packages.
Configure MongoDB:
Ensure MongoDB is running and accessible at MONGO_URI.
Populate collections (e.g., swaps, aave_users) with relevant DeFi data.


Set Up .env:
Update .env with your MongoDB URI and LLM API key.


Run the Application:uvicorn main:app --host 0.0.0.0 --port 8001

This starts the FastAPI server at http://localhost:8001.

Testing Queries
Use PowerShell, curl, or any HTTP client to send queries to the /query endpoint:

Valid LP Query:$response = Invoke-RestMethod -Uri http://localhost:8001/query -Method POST -ContentType "application/json" -Body '{"prompt": "Top users on Aave with scores between 200 and 300"}'
$response | Format-List

Expected Output:The top users on Aave with zScores between 200 and 300 exhibit moderate activity, with consistent lending and borrowing patterns. 
Wallets Analyzed:
- 0x789...xyz
- 0xabc...123
These users maintain stable collateral ratios and regular repayments, indicating reliable creditworthiness.


Valid DEX Query:$response = Invoke-RestMethod -Uri http://localhost:8001/query -Method POST -ContentType "application/json" -Body '{"prompt": "List wallets whose single largest swap exceeded $100,000"}'
$response | Format-List

Expected Output:The following wallets executed swaps exceeding $100,000 in value, indicating significant trading activity:
Wallets Analyzed:
- 0x123...abc: Max swap $150,000
- 0x456...def: Max swap $120,000
These wallets demonstrate high-volume trading, with a preference for large transactions, potentially indicating whale activity or arbitrage strategies.


Invalid Query (e.g., invalid protocol):$response = Invoke-RestMethod -Uri http://localhost:8001/query -Method POST -ContentType "application/json" -Body '{"prompt": "Top users on InvalidProtocol with scores between 20000 and 30000"}'
$response | Format-List

Expected Output:Sorry, I can't answer this. Instead, try asking something like:
- Compare Aave and Compound V2 activity
- What’s the lending behavior of ****************************************** on Aave?
- Analyze the risk profile of wallet 0x000abc123def... on Compound V3?
- List wallets whose single largest swap exceeded $100k
- What tokens are most commonly swapped


Off-Topic Query:$response = Invoke-RestMethod -Uri http://localhost:8001/query -Method POST -ContentType "application/json" -Body '{"prompt": "I love you"}'
$response | Format-List

Expected Output:Aw, that's sweet! Sorry, I can't answer this. Instead, try asking something like:
- Compare Aave and Compound V2 activity
- What’s the lending behavior of ****************************************** on Aave?
- Analyze the risk profile of wallet 0x000abc123def... on Compound V3?
- List wallets whose single largest swap exceeded $100k
- What tokens are most commonly swapped



Troubleshooting

Truncated Output: If the response is truncated (e.g., …), use:$response | Out-File response.txt

Or check app.log for the full message:tail -f app.log


"high" Error: If queries like "Top users on Aave with scores between 200 and 300" return errors, check:
app.log for parsed parameters (Parsed: {vars(parsed)}) and LLM prompt (LP prompt generated: ...).
query_parser.py output:from query_parser import parse_prompt_llm
parsed = parse_prompt_llm("Top users on Aave with scores between 200 and 300")
print(vars(parsed))

Expected: {'use_case': 'lp_score_filtered_wallets', 'wallet_address': None, 'protocols': ['aave'], 'score_range': [200, 300], 'top_n': 10}
MongoDB data:db.aave_users.find({"zScore": {"$gte": 200, "$lte": 300}}).limit(10)




MongoDB Errors: Verify collections (swaps, aave_users) exist and contain data:db.getCollectionNames()



Notes

Error Handling: All errors (DEX, LP, or classification) return only the ERROR_SUGGESTION_MESSAGE to users, with technical details logged to app.log.
Scalability: The modular design allows adding new use cases or protocols by updating lp_usecases.py or query_parser.py.
Dependencies: Ensure the LLM API key and MongoDB URI are correctly configured in .env.
Logging: All actions (classification, pipeline execution, errors) are logged to app.log for debugging.

THINGS REMAINING TO DO 
1.IMPROVE THE LP LOGIC
CURENTLY LP LOGIC WORKS LIKE : IF QUERRY IS CLASSIFIED AS LP ----> CHEKCS IF IT CAN BE ANSWERED USING Z PASS----> IF YES CLASS Z PASS API----> IF NOT THEN USE MONGO DB LOGIC JUST LIKE WE USE FOR DEX
THE LOGIC IS WORKING BUT THE LLM PROMPST AND INPUTS CAN BE REFINED 





2.FROM SENTIENT FRAMEWORK POV 
REFFER THIS REPOSITORY 2 FILES 


https://github.com/zerufinance/sentient_agent
A. agent.py
b. wrappedagent.py


so bascally first make a agent.py completely anf then make a wrappedagent.py for it which usese sentient framework just like b.wrappedagent.py is made using sentient framework and for testing use sentient agent client 
https://github.com/sentient-agi/Sentient-Agent-Client
for testing run a fast api server on the wrapped agent agent.py u created and then acces that route on the sentient agent client 
