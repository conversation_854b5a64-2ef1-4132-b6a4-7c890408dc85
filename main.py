"""
Main entry point for DeFi Analytics Agent
This file maintains backward compatibility with existing deployment scripts
"""

import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import the FastAPI app from the organized structure
from src.api.main import app

# For backward compatibility, expose the app at module level
__all__ = ['app']

if __name__ == "__main__":
    import uvicorn
    from config.settings import API_HOST, API_PORT

    uvicorn.run(
        "main:app",
        host=API_HOST,
        port=API_PORT,
        reload=True
    )
