#!/usr/bin/env python3
"""
Test script to verify the reorganized codebase structure works correctly
"""

import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all major imports work correctly"""
    try:
        print("🧪 Testing imports...")
        
        # Test config imports
        print("  ✓ Testing config imports...")
        from config.settings import CORS_ORIGINS, ERROR_SUGGESTION_MESSAGE
        print(f"    - CORS origins: {len(CORS_ORIGINS)} configured")
        
        # Test database imports
        print("  ✓ Testing database imports...")
        from src.database.connection import get_db
        print("    - Database connection module loaded")
        
        # Test utility imports
        print("  ✓ Testing utility imports...")
        from src.utils.llm_utils import generate_llm_prompt
        print("    - LLM utilities loaded")
        
        # Test main app import
        print("  ✓ Testing main app import...")
        from main import app
        print("    - FastAPI app loaded successfully")
        
        print("✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_app_structure():
    """Test that the app structure is correct"""
    try:
        print("🏗️ Testing app structure...")
        
        from main import app
        
        # Check if routes are available
        routes = [route.path for route in app.routes]
        expected_routes = ["/health", "/query"]
        
        for expected in expected_routes:
            if expected in routes:
                print(f"  ✓ Route {expected} found")
            else:
                print(f"  ❌ Route {expected} missing")
                return False
        
        print("✅ App structure test passed!")
        return True
        
    except Exception as e:
        print(f"❌ App structure test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing DeFi Analytics Agent Reorganized Structure")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("App Structure Tests", test_app_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        if test_func():
            passed += 1
        print("-" * 40)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The reorganized structure is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
