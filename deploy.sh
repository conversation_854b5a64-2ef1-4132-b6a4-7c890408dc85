#!/bin/bash

# Complete Deployment Script for DeFi Analytics Agent
# This script handles the complete deployment with HTTPS and service setup

set -e

echo "🚀 Complete Deployment Setup for DeFi Analytics Agent"
echo "====================================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root. Please run as a regular user with sudo privileges."
   exit 1
fi

# Make scripts executable
print_status "Making scripts executable..."
chmod +x setup-https.sh
chmod +x run-production.sh

# Step 1: Set up the systemd service
print_status "Setting up systemd service..."
sudo cp defi-agent.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable defi-agent

# Step 2: Start the FastAPI service
print_status "Starting DeFi Analytics Agent service..."
sudo systemctl start defi-agent

# Wait a moment for the service to start
sleep 5

# Check if service is running
if sudo systemctl is-active --quiet defi-agent; then
    print_success "DeFi Analytics Agent service is running"
else
    print_error "Failed to start DeFi Analytics Agent service"
    sudo systemctl status defi-agent
    exit 1
fi

# Step 3: Set up HTTPS
print_status "Setting up HTTPS and Nginx reverse proxy..."
./setup-https.sh

# Step 4: Test the deployment
print_status "Testing the deployment..."

# Test HTTP redirect
print_status "Testing HTTP to HTTPS redirect..."
HTTP_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://agentapi.myzscore.ai/health || echo "000")
if [ "$HTTP_RESPONSE" = "301" ] || [ "$HTTP_RESPONSE" = "302" ]; then
    print_success "HTTP to HTTPS redirect is working"
else
    print_warning "HTTP redirect test returned: $HTTP_RESPONSE"
fi

# Test HTTPS endpoint
print_status "Testing HTTPS endpoint..."
HTTPS_RESPONSE=$(curl -s -k https://agentapi.myzscore.ai/health || echo "Failed")
if [[ "$HTTPS_RESPONSE" == *"DEX Analyst Agent is running"* ]]; then
    print_success "HTTPS endpoint is working correctly"
else
    print_warning "HTTPS endpoint test failed: $HTTPS_RESPONSE"
fi

# Test CORS
print_status "Testing CORS configuration..."
CORS_RESPONSE=$(curl -s -k -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: POST" -H "Access-Control-Request-Headers: Content-Type" -X OPTIONS https://agentapi.myzscore.ai/query || echo "Failed")
if [[ "$CORS_RESPONSE" == "" ]]; then
    print_success "CORS preflight request is working"
else
    print_warning "CORS test response: $CORS_RESPONSE"
fi

print_success "Deployment completed successfully!"
echo ""
echo "🎉 Your DeFi Analytics Agent is now deployed and running!"
echo ""
echo "📡 Available endpoints:"
echo "  - Health check: https://agentapi.myzscore.ai/health"
echo "  - API endpoint: https://agentapi.myzscore.ai/query"
echo ""
echo "🌐 CORS is configured for:"
echo "  - http://localhost:3000"
echo "  - https://localhost:3000"
echo "  - https://agent.myzscore.ai"
echo ""
echo "🔧 Service management commands:"
echo "  - Check status: sudo systemctl status defi-agent"
echo "  - View logs: sudo journalctl -u defi-agent -f"
echo "  - Restart: sudo systemctl restart defi-agent"
echo "  - Stop: sudo systemctl stop defi-agent"
echo ""
echo "📊 Nginx logs:"
echo "  - Access: sudo tail -f /var/log/nginx/agentapi.myzscore.ai.access.log"
echo "  - Error: sudo tail -f /var/log/nginx/agentapi.myzscore.ai.error.log"
echo ""
print_warning "Make sure your domain DNS is pointing to this server's IP address!"
print_warning "Test your API from your frontend application at localhost:3000"
