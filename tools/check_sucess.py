"""
check_success.py
Bypasses MongoDB's memory limit by grouping in Python.
"""

import os
import json
from dotenv import load_dotenv
from pymongo import MongoClient
import logging
from collections import defaultdict

# Configure logging
logging.basicConfig(
    filename="app.log",
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Load environment variables
load_dotenv()
MONGO_URI = os.getenv("MONGO_URI")
if not MONGO_URI:
    logging.error("MONGO_URI not found in .env")
    print("❌ Error: MONGO_URI not found in .env")
    exit(1)

def run_lightweight_query():
    """Fetch filtered borrow/repay records without grouping."""
    try:
        client = MongoClient(MONGO_URI)
        db = client["test-tx-collector"]
        collection = db["success"]
        print("📡 Connected to:", db.name + "." + collection.name)

        pipeline = [
            {"$unwind": {"path": "$protocolScores", "preserveNullAndEmptyArrays": False}},
            {"$match": {"protocolScores.name": {"$regex": "^aave$", "$options": "i"}}},
            {"$unwind": {"path": "$protocolScores.user_out", "preserveNullAndEmptyArrays": True}},
            {"$project": {
                "wallet_address": 1,
                "borrow": {
                    "$cond": [
                        {"$gt": ["$protocolScores.user_out.borrow", 0]},
                        "$protocolScores.user_out.borrow",
                        0
                    ]
                },
                "repay": {
                    "$cond": [
                        {"$gt": ["$protocolScores.user_out.repay", 0]},
                        "$protocolScores.user_out.repay",
                        0
                    ]
                }
            }},
            {"$match": {
                "$or": [
                    {"borrow": {"$gt": 0}},
                    {"repay": {"$gt": 0}}
                ]
            }}
        ]

        cursor = collection.aggregate(pipeline, allowDiskUse=True)
        records = list(cursor)
        client.close()
        print(f"📦 Fetched {len(records)} borrow/repay records")
        return records

    except Exception as e:
        print("❌ Query failed:", str(e))
        logging.error(f"Query failed: {str(e)}")
        return []

def group_and_summarize(records):
    """Group records by wallet and compute total borrow + repay."""
    totals = defaultdict(float)
    for doc in records:
        wallet = doc.get("wallet_address")
        borrow = doc.get("borrow", 0)
        repay = doc.get("repay", 0)
        totals[wallet] += borrow + repay

    # Sort and get top 10
    top_wallets = sorted(totals.items(), key=lambda x: x[1], reverse=True)[:10]
    return [{"wallet_address": wallet, "total_borrow_repay": round(amount, 2)} for wallet, amount in top_wallets]

def main():
    print("📜 Running fallback version (grouping in Python)...")
    records = run_lightweight_query()
    if not records:
        print("❌ No data to process.")
        return

    top_wallets = group_and_summarize(records)
    print("🏆 Top 10 Aave Wallets by Borrow + Repay:\n")
    print(json.dumps(top_wallets, indent=2))

if __name__ == "__main__":
    main()
